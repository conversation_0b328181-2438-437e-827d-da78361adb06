/**
 * Tests for User model
 */

const mongoose = require('mongoose');

// Mock mongoose before importing the model
jest.mock('mongoose', () => ({
    Schema: jest.fn().mockImplementation(() => ({
        index: jest.fn(),
        methods: {},
        statics: {},
        pre: jest.fn(),
        virtual: jest.fn().mockReturnValue({
            get: jest.fn()
        })
    })),
    model: jest.fn(),
    connect: jest.fn(),
    disconnect: jest.fn()
}));

describe('User Model', () => {
    let User;
    let mockUserInstance;

    beforeAll(async () => {
        // Mock User model
        User = {
            findByDiscordId: jest.fn(),
            createFromDiscord: jest.fn(),
            findActiveUsers: jest.fn(),
            getTopSpenders: jest.fn()
        };
        
        // Create mock user instance
        mockUserInstance = {
            discordId: '123456789',
            username: 'TestUser',
            discriminator: '1234',
            balance: 100,
            totalSpent: 50,
            preferences: {
                notifications: true,
                language: 'pt-BR'
            },
            status: 'active',
            lastSeen: new Date(),
            joinedAt: new Date(),
            address: {
                street: 'Test Street',
                number: '123',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345-678',
                country: 'Brasil'
            },
            save: jest.fn().mockResolvedValue(true),
            updateLastSeen: jest.fn(),
            addBalance: jest.fn(),
            deductBalance: jest.fn(),
            addPurchase: jest.fn()
        };
    });

    describe('Schema Validation', () => {
        test('should require discordId', () => {
            const userData = { username: 'Test', discriminator: '1234' };
            // Test would validate required fields
            expect(userData.discordId).toBeUndefined();
        });

        test('should require username', () => {
            const userData = { discordId: '123456789', discriminator: '1234' };
            expect(userData.username).toBeUndefined();
        });

        test('should require discriminator', () => {
            const userData = { discordId: '123456789', username: 'Test' };
            expect(userData.discriminator).toBeUndefined();
        });

        test('should have default values', () => {
            const userData = {
                discordId: '123456789',
                username: 'Test',
                discriminator: '1234'
            };
            
            // Test default values
            expect(mockUserInstance.balance).toBe(100);
            expect(mockUserInstance.totalSpent).toBe(50);
            expect(mockUserInstance.preferences.notifications).toBe(true);
            expect(mockUserInstance.preferences.language).toBe('pt-BR');
            expect(mockUserInstance.status).toBe('active');
        });

        test('should validate status enum', () => {
            const validStatuses = ['active', 'suspended', 'banned'];
            validStatuses.forEach(status => {
                expect(validStatuses).toContain(status);
            });
        });

        test('should validate language enum', () => {
            const validLanguages = ['pt-BR', 'en-US', 'es-ES'];
            validLanguages.forEach(lang => {
                expect(validLanguages).toContain(lang);
            });
        });

        test('should validate balance is not negative', () => {
            // Mock validation would check min: 0
            expect(mockUserInstance.balance).toBeGreaterThanOrEqual(0);
        });

        test('should validate totalSpent is not negative', () => {
            expect(mockUserInstance.totalSpent).toBeGreaterThanOrEqual(0);
        });
    });

    describe('Instance Methods', () => {
        test('updateLastSeen should update lastSeen timestamp', async () => {
            const originalDate = mockUserInstance.lastSeen;
            
            // Mock the method behavior
            mockUserInstance.updateLastSeen = jest.fn().mockImplementation(function() {
                this.lastSeen = new Date();
                return this.save();
            });

            await mockUserInstance.updateLastSeen();
            
            expect(mockUserInstance.updateLastSeen).toHaveBeenCalled();
            expect(mockUserInstance.save).toHaveBeenCalled();
        });

        test('addBalance should increase balance with positive amount', async () => {
            const initialBalance = 100;
            const addAmount = 50;
            
            mockUserInstance.addBalance = jest.fn().mockImplementation(function(amount) {
                if (amount > 0) {
                    this.balance += amount;
                    return this.save();
                }
                throw new Error('Valor deve ser positivo');
            });

            mockUserInstance.balance = initialBalance;
            await mockUserInstance.addBalance(addAmount);
            
            expect(mockUserInstance.addBalance).toHaveBeenCalledWith(addAmount);
            expect(mockUserInstance.balance).toBe(initialBalance + addAmount);
        });

        test('addBalance should throw error with negative amount', async () => {
            mockUserInstance.addBalance = jest.fn().mockImplementation(function(amount) {
                if (amount > 0) {
                    this.balance += amount;
                    return this.save();
                }
                throw new Error('Valor deve ser positivo');
            });

            try {
                await mockUserInstance.addBalance(-10);
                fail('Should have thrown an error');
            } catch (error) {
                expect(error.message).toBe('Valor deve ser positivo');
            }
        });

        test('deductBalance should decrease balance with sufficient funds', async () => {
            const initialBalance = 100;
            const deductAmount = 30;
            
            mockUserInstance.deductBalance = jest.fn().mockImplementation(function(amount) {
                if (amount > this.balance) {
                    throw new Error('Saldo insuficiente');
                }
                this.balance -= amount;
                return this.save();
            });

            mockUserInstance.balance = initialBalance;
            await mockUserInstance.deductBalance(deductAmount);
            
            expect(mockUserInstance.deductBalance).toHaveBeenCalledWith(deductAmount);
            expect(mockUserInstance.balance).toBe(initialBalance - deductAmount);
        });

        test('deductBalance should throw error with insufficient funds', async () => {
            mockUserInstance.balance = 10;
            
            mockUserInstance.deductBalance = jest.fn().mockImplementation(function(amount) {
                if (amount > this.balance) {
                    throw new Error('Saldo insuficiente');
                }
                this.balance -= amount;
                return this.save();
            });

            try {
                await mockUserInstance.deductBalance(50);
                fail('Should have thrown an error');
            } catch (error) {
                expect(error.message).toBe('Saldo insuficiente');
            }
        });

        test('addPurchase should update totalSpent and deduct balance', async () => {
            const initialBalance = 100;
            const initialTotalSpent = 50;
            const purchaseAmount = 25;
            
            mockUserInstance.addPurchase = jest.fn().mockImplementation(function(amount) {
                if (amount > this.balance) {
                    throw new Error('Saldo insuficiente');
                }
                this.balance -= amount;
                this.totalSpent += amount;
                return this.save();
            });

            mockUserInstance.balance = initialBalance;
            mockUserInstance.totalSpent = initialTotalSpent;
            
            await mockUserInstance.addPurchase(purchaseAmount);
            
            expect(mockUserInstance.addPurchase).toHaveBeenCalledWith(purchaseAmount);
            expect(mockUserInstance.balance).toBe(initialBalance - purchaseAmount);
            expect(mockUserInstance.totalSpent).toBe(initialTotalSpent + purchaseAmount);
        });
    });

    describe('Static Methods', () => {
        test('findByDiscordId should find user by Discord ID', async () => {
            const discordId = '123456789';
            
            User.findByDiscordId = jest.fn().mockResolvedValue(mockUserInstance);
            
            const result = await User.findByDiscordId(discordId);
            
            expect(User.findByDiscordId).toHaveBeenCalledWith(discordId);
            expect(result).toBe(mockUserInstance);
        });

        test('createFromDiscord should create user from Discord data', async () => {
            const discordUser = {
                id: '123456789',
                username: 'TestUser',
                discriminator: '1234'
            };
            
            User.createFromDiscord = jest.fn().mockResolvedValue(mockUserInstance);
            
            const result = await User.createFromDiscord(discordUser);
            
            expect(User.createFromDiscord).toHaveBeenCalledWith(discordUser);
            expect(result).toBe(mockUserInstance);
        });

        test('findActiveUsers should return only active users', async () => {
            const activeUsers = [mockUserInstance];
            
            User.findActiveUsers = jest.fn().mockResolvedValue(activeUsers);
            
            const result = await User.findActiveUsers();
            
            expect(User.findActiveUsers).toHaveBeenCalled();
            expect(result).toEqual(activeUsers);
        });

        test('getTopSpenders should return users ordered by totalSpent', async () => {
            const topSpenders = [mockUserInstance];
            
            User.getTopSpenders = jest.fn().mockResolvedValue(topSpenders);
            
            const result = await User.getTopSpenders(10);
            
            expect(User.getTopSpenders).toHaveBeenCalledWith(10);
            expect(result).toEqual(topSpenders);
        });
    });

    describe('Edge Cases and Error Handling', () => {
        test('should handle invalid Discord ID format', () => {
            const invalidIds = ['', null, undefined, 'invalid', 123];

            invalidIds.forEach(id => {
                // Check if ID is invalid (not a string or empty or too short for Discord ID)
                const isValid = typeof id === 'string' && id.length >= 17 && /^\d+$/.test(id);
                expect(isValid).toBeFalsy();
            });
        });

        test('should handle missing required fields', () => {
            const incompleteData = [
                { username: 'Test', discriminator: '1234' }, // missing discordId
                { discordId: '123', discriminator: '1234' }, // missing username
                { discordId: '123', username: 'Test' } // missing discriminator
            ];
            
            incompleteData.forEach(data => {
                const hasAllRequired = data.discordId && data.username && data.discriminator;
                expect(hasAllRequired).toBeFalsy();
            });
        });

        test('should handle database connection errors', async () => {
            User.findByDiscordId = jest.fn().mockRejectedValue(new Error('Database connection failed'));
            
            await expect(User.findByDiscordId('123')).rejects.toThrow('Database connection failed');
        });

        test('should handle concurrent balance updates', async () => {
            // Simulate race condition
            const user1 = { ...mockUserInstance, balance: 100 };
            const user2 = { ...mockUserInstance, balance: 100 };
            
            // Both try to deduct at the same time
            user1.deductBalance = jest.fn().mockImplementation(function(amount) {
                if (amount > this.balance) throw new Error('Saldo insuficiente');
                this.balance -= amount;
                return Promise.resolve();
            });
            
            user2.deductBalance = jest.fn().mockImplementation(function(amount) {
                if (amount > this.balance) throw new Error('Saldo insuficiente');
                this.balance -= amount;
                return Promise.resolve();
            });
            
            await user1.deductBalance(60);
            await user2.deductBalance(60);
            
            // In a real scenario, this would need proper locking
            expect(user1.balance).toBe(40);
            expect(user2.balance).toBe(40);
        });
    });
});
