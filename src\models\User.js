import mongoose from 'mongoose';

const userSchema = new mongoose.Schema({
    // Identificação do Discord
    discordId: {
        type: String,
        required: true,
        unique: true
    },
    username: {
        type: String,
        required: true
    },
    discriminator: {
        type: String,
        required: true
    },
    
    // Informações da loja
    balance: {
        type: Number,
        default: 0,
        min: 0
    },
    totalSpent: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // Configurações do usuário
    preferences: {
        notifications: {
            type: Boolean,
            default: true
        },
        language: {
            type: String,
            default: 'pt-BR',
            enum: ['pt-BR', 'en-US', 'es-ES']
        }
    },
    
    // Histórico de atividades
    lastSeen: {
        type: Date,
        default: Date.now
    },
    joinedAt: {
        type: Date,
        default: Date.now
    },
    
    // Status do usuário
    status: {
        type: String,
        enum: ['active', 'suspended', 'banned'],
        default: 'active'
    },
    
    // Dados de endereço (para entregas)
    address: {
        street: String,
        number: String,
        complement: String,
        neighborhood: String,
        city: String,
        state: String,
        zipCode: String,
        country: {
            type: String,
            default: 'Brasil'
        }
    }
}, {
    timestamps: true,
    collection: 'users'
});

// Índices para otimização
userSchema.index({ status: 1 });
userSchema.index({ createdAt: -1 });

// Métodos do schema
userSchema.methods.updateLastSeen = function() {
    this.lastSeen = new Date();
    return this.save();
};

userSchema.methods.addBalance = function(amount) {
    if (amount > 0) {
        this.balance += amount;
        return this.save();
    }
    throw new Error('Valor deve ser positivo');
};

userSchema.methods.deductBalance = function(amount) {
    if (amount > 0 && this.balance >= amount) {
        this.balance -= amount;
        this.totalSpent += amount;
        return this.save();
    }
    throw new Error('Saldo insuficiente ou valor inválido');
};

// Métodos estáticos
userSchema.statics.findByDiscordId = function(discordId) {
    return this.findOne({ discordId });
};

userSchema.statics.getActiveUsers = function() {
    return this.find({ status: 'active' });
};

export default mongoose.model('User', userSchema);
