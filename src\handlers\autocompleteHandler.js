import { logger } from '../utils/logger.js';
import Store from '../models/Store.js';

/**
 * Manipula interações de autocomplete
 * @param {AutocompleteInteraction} interaction 
 */
export async function handleAutocomplete(interaction) {
    const { commandName, options } = interaction;

    try {
        // Verifica se a interação já foi respondida
        if (interaction.responded) {
            logger.warn(`Tentativa de responder autocomplete já respondido para ${commandName}`);
            return;
        }

        if (commandName === 'reenviar-lojas') {
            const focusedOption = options.getFocused(true);
            
            if (focusedOption.name === 'loja') {
                // Busca todas as lojas do servidor
                const stores = await Store.find({ guildId: interaction.guild.id });
                
                // Filtra lojas baseado no que o usuário digitou
                const filtered = stores.filter(store => 
                    store.name.toLowerCase().includes(focusedOption.value.toLowerCase())
                ).slice(0, 25); // Discord limita a 25 opções
                
                // Cria as opções de resposta
                const choices = filtered.map(store => ({
                    name: store.name,
                    value: store.name
                }));
                
                await interaction.respond(choices);
            }
        }
    } catch (error) {
        logger.error(`Erro no autocomplete para ${commandName}:`, error);
        
        // Só tenta responder se a interação ainda não foi respondida
        if (!interaction.responded) {
            try {
                await interaction.respond([]);
            } catch (respondError) {
                logger.error('Erro ao responder autocomplete com lista vazia:', respondError);
            }
        }
    }
}