/**
 * Tests for rate limiter utility
 */

describe('Rate Limiter', () => {
    let rateLimiter;

    beforeAll(async () => {
        // Mock rate limiter implementation
        rateLimiter = {
            requests: new Map(),
            limits: {
                default: { requests: 5, window: 60000 }, // 5 requests per minute
                ping: { requests: 10, window: 30000 },   // 10 requests per 30 seconds
                help: { requests: 3, window: 60000 }     // 3 requests per minute
            },

            isRateLimited(key) {
                const now = Date.now();
                const requests = this.requests.get(key) || [];
                
                // Get command from key
                const command = key.split(':')[2] || 'default';
                const limit = this.limits[command] || this.limits.default;
                
                // Remove old requests outside the window
                const validRequests = requests.filter(timestamp => 
                    now - timestamp < limit.window
                );
                
                // Update the requests array
                this.requests.set(key, validRequests);
                
                return validRequests.length >= limit.requests;
            },

            addRequest(key) {
                const now = Date.now();
                const requests = this.requests.get(key) || [];
                
                requests.push(now);
                this.requests.set(key, requests);
            },

            getRemainingTime(key) {
                const now = Date.now();
                const requests = this.requests.get(key) || [];

                if (requests.length === 0) return 0;

                const command = key.split(':')[2] || 'default';
                const limit = this.limits[command] || this.limits.default;

                // Filter out expired requests
                const validRequests = requests.filter(timestamp =>
                    now - timestamp < limit.window
                );

                // If not rate limited, return 0
                if (validRequests.length < limit.requests) return 0;

                const oldestRequest = Math.min(...validRequests);
                const timeUntilReset = limit.window - (now - oldestRequest);

                return Math.ceil(timeUntilReset / 1000);
            },

            cleanup() {
                const now = Date.now();
                
                for (const [key, requests] of this.requests.entries()) {
                    const command = key.split(':')[2] || 'default';
                    const limit = this.limits[command] || this.limits.default;
                    
                    const validRequests = requests.filter(timestamp => 
                        now - timestamp < limit.window
                    );
                    
                    if (validRequests.length === 0) {
                        this.requests.delete(key);
                    } else {
                        this.requests.set(key, validRequests);
                    }
                }
            },

            reset(key) {
                if (key) {
                    this.requests.delete(key);
                } else {
                    this.requests.clear();
                }
            },

            getStats() {
                return {
                    totalKeys: this.requests.size,
                    totalRequests: Array.from(this.requests.values())
                        .reduce((sum, requests) => sum + requests.length, 0)
                };
            }
        };
    });

    beforeEach(() => {
        rateLimiter.reset();
    });

    describe('Basic Rate Limiting', () => {
        test('should not rate limit first request', () => {
            const key = 'user123:guild456:ping';
            
            expect(rateLimiter.isRateLimited(key)).toBe(false);
        });

        test('should not rate limit requests within limit', () => {
            const key = 'user123:guild456:ping';
            
            // Add 9 requests (limit is 10 for ping)
            for (let i = 0; i < 9; i++) {
                rateLimiter.addRequest(key);
                expect(rateLimiter.isRateLimited(key)).toBe(false);
            }
        });

        test('should rate limit when exceeding limit', () => {
            const key = 'user123:guild456:ping';
            
            // Add 10 requests (limit is 10 for ping)
            for (let i = 0; i < 10; i++) {
                rateLimiter.addRequest(key);
            }
            
            expect(rateLimiter.isRateLimited(key)).toBe(true);
        });

        test('should use default limit for unknown commands', () => {
            const key = 'user123:guild456:unknown';
            
            // Add 5 requests (default limit is 5)
            for (let i = 0; i < 5; i++) {
                rateLimiter.addRequest(key);
            }
            
            expect(rateLimiter.isRateLimited(key)).toBe(true);
        });

        test('should handle different limits for different commands', () => {
            const pingKey = 'user123:guild456:ping';
            const helpKey = 'user123:guild456:help';
            
            // Ping allows 10 requests
            for (let i = 0; i < 10; i++) {
                rateLimiter.addRequest(pingKey);
            }
            expect(rateLimiter.isRateLimited(pingKey)).toBe(true);
            
            // Help allows only 3 requests
            for (let i = 0; i < 3; i++) {
                rateLimiter.addRequest(helpKey);
            }
            expect(rateLimiter.isRateLimited(helpKey)).toBe(true);
        });
    });

    describe('Time Window Management', () => {
        test('should reset rate limit after time window', () => {
            const key = 'user123:guild456:ping';
            const originalNow = Date.now;
            let mockTime = Date.now();
            
            // Mock Date.now
            Date.now = jest.fn(() => mockTime);
            
            // Fill up the rate limit
            for (let i = 0; i < 10; i++) {
                rateLimiter.addRequest(key);
            }
            expect(rateLimiter.isRateLimited(key)).toBe(true);
            
            // Advance time beyond the window (30 seconds for ping)
            mockTime += 31000;
            
            expect(rateLimiter.isRateLimited(key)).toBe(false);
            
            // Restore Date.now
            Date.now = originalNow;
        });

        test('should partially reset rate limit as time passes', () => {
            const key = 'user123:guild456:ping';
            const originalNow = Date.now;
            let mockTime = Date.now();
            
            Date.now = jest.fn(() => mockTime);
            
            // Add 5 requests
            for (let i = 0; i < 5; i++) {
                rateLimiter.addRequest(key);
                mockTime += 1000; // 1 second apart
            }
            
            // Advance time by 15 seconds (half the window)
            mockTime += 15000;
            
            // Add 5 more requests
            for (let i = 0; i < 5; i++) {
                rateLimiter.addRequest(key);
            }
            
            // Should be rate limited (10 requests total)
            expect(rateLimiter.isRateLimited(key)).toBe(true);
            
            // Advance time by another 16 seconds (total 31 seconds)
            // First 5 requests should be expired
            mockTime += 16000;
            
            expect(rateLimiter.isRateLimited(key)).toBe(false);
            
            Date.now = originalNow;
        });

        test('should calculate correct remaining time', () => {
            const key = 'user123:guild456:ping';
            const originalNow = Date.now;
            let mockTime = Date.now();
            
            Date.now = jest.fn(() => mockTime);
            
            // Fill up the rate limit
            for (let i = 0; i < 10; i++) {
                rateLimiter.addRequest(key);
            }
            
            // Should have 30 seconds remaining (ping window is 30 seconds)
            const remainingTime = rateLimiter.getRemainingTime(key);
            expect(remainingTime).toBe(30);
            
            // Advance time by 10 seconds
            mockTime += 10000;
            
            const newRemainingTime = rateLimiter.getRemainingTime(key);
            expect(newRemainingTime).toBe(20);
            
            Date.now = originalNow;
        });

        test('should return 0 remaining time when not rate limited', () => {
            const key = 'user123:guild456:different';

            // Key that hasn't been used should return 0
            expect(rateLimiter.getRemainingTime(key)).toBe(0);

            // Add a few requests (below limit) but don't exceed the limit
            for (let i = 0; i < 3; i++) {
                rateLimiter.addRequest(key);
            }

            // Still below limit, should return 0
            expect(rateLimiter.getRemainingTime(key)).toBe(0);
        });
    });

    describe('Key Management', () => {
        test('should handle different users separately', () => {
            const user1Key = 'user111:guild456:ping';
            const user2Key = 'user222:guild456:ping';
            
            // Fill up rate limit for user1
            for (let i = 0; i < 10; i++) {
                rateLimiter.addRequest(user1Key);
            }
            
            expect(rateLimiter.isRateLimited(user1Key)).toBe(true);
            expect(rateLimiter.isRateLimited(user2Key)).toBe(false);
        });

        test('should handle different guilds separately', () => {
            const guild1Key = 'user123:guild111:ping';
            const guild2Key = 'user123:guild222:ping';
            
            // Fill up rate limit for guild1
            for (let i = 0; i < 10; i++) {
                rateLimiter.addRequest(guild1Key);
            }
            
            expect(rateLimiter.isRateLimited(guild1Key)).toBe(true);
            expect(rateLimiter.isRateLimited(guild2Key)).toBe(false);
        });

        test('should handle different commands separately', () => {
            const pingKey = 'user123:guild456:ping';
            const helpKey = 'user123:guild456:help';
            
            // Fill up rate limit for ping
            for (let i = 0; i < 10; i++) {
                rateLimiter.addRequest(pingKey);
            }
            
            expect(rateLimiter.isRateLimited(pingKey)).toBe(true);
            expect(rateLimiter.isRateLimited(helpKey)).toBe(false);
        });

        test('should handle DM keys properly', () => {
            const dmKey = 'user123:dm:ping';
            const guildKey = 'user123:guild456:ping';
            
            // Fill up rate limit for DM
            for (let i = 0; i < 10; i++) {
                rateLimiter.addRequest(dmKey);
            }
            
            expect(rateLimiter.isRateLimited(dmKey)).toBe(true);
            expect(rateLimiter.isRateLimited(guildKey)).toBe(false);
        });
    });

    describe('Cleanup and Maintenance', () => {
        test('should cleanup expired requests', () => {
            const key = 'user123:guild456:ping';
            const originalNow = Date.now;
            let mockTime = Date.now();
            
            Date.now = jest.fn(() => mockTime);
            
            // Add some requests
            for (let i = 0; i < 5; i++) {
                rateLimiter.addRequest(key);
            }
            
            expect(rateLimiter.getStats().totalRequests).toBe(5);
            
            // Advance time beyond window
            mockTime += 31000;
            
            rateLimiter.cleanup();
            
            expect(rateLimiter.getStats().totalRequests).toBe(0);
            expect(rateLimiter.getStats().totalKeys).toBe(0);
            
            Date.now = originalNow;
        });

        test('should cleanup only expired requests', () => {
            const key1 = 'user123:guild456:ping';
            const key2 = 'user456:guild456:ping';
            const originalNow = Date.now;
            let mockTime = Date.now();
            
            Date.now = jest.fn(() => mockTime);
            
            // Add requests to key1
            for (let i = 0; i < 3; i++) {
                rateLimiter.addRequest(key1);
            }
            
            // Advance time
            mockTime += 20000;
            
            // Add requests to key2
            for (let i = 0; i < 3; i++) {
                rateLimiter.addRequest(key2);
            }
            
            // Advance time beyond key1's window but not key2's
            mockTime += 15000; // Total 35 seconds
            
            rateLimiter.cleanup();
            
            // key1 should be cleaned up, key2 should remain
            expect(rateLimiter.getStats().totalKeys).toBe(1);
            expect(rateLimiter.getStats().totalRequests).toBe(3);
            
            Date.now = originalNow;
        });

        test('should reset specific key', () => {
            const key1 = 'user123:guild456:ping';
            const key2 = 'user456:guild456:ping';
            
            // Add requests to both keys
            for (let i = 0; i < 5; i++) {
                rateLimiter.addRequest(key1);
                rateLimiter.addRequest(key2);
            }
            
            expect(rateLimiter.getStats().totalRequests).toBe(10);
            
            rateLimiter.reset(key1);
            
            expect(rateLimiter.getStats().totalRequests).toBe(5);
            expect(rateLimiter.isRateLimited(key1)).toBe(false);
            expect(rateLimiter.isRateLimited(key2)).toBe(false); // 5 requests, limit is 10 for ping
        });

        test('should reset all keys', () => {
            const key1 = 'user123:guild456:ping';
            const key2 = 'user456:guild456:help';
            
            // Add requests to both keys
            for (let i = 0; i < 5; i++) {
                rateLimiter.addRequest(key1);
                rateLimiter.addRequest(key2);
            }
            
            expect(rateLimiter.getStats().totalRequests).toBe(10);
            
            rateLimiter.reset();
            
            expect(rateLimiter.getStats().totalRequests).toBe(0);
            expect(rateLimiter.getStats().totalKeys).toBe(0);
        });
    });

    describe('Statistics and Monitoring', () => {
        test('should provide accurate statistics', () => {
            const keys = [
                'user123:guild456:ping',
                'user456:guild456:help',
                'user789:guild456:info'
            ];
            
            // Add different amounts of requests to each key
            for (let i = 0; i < 3; i++) {
                rateLimiter.addRequest(keys[0]);
            }
            for (let i = 0; i < 2; i++) {
                rateLimiter.addRequest(keys[1]);
            }
            for (let i = 0; i < 4; i++) {
                rateLimiter.addRequest(keys[2]);
            }
            
            const stats = rateLimiter.getStats();
            expect(stats.totalKeys).toBe(3);
            expect(stats.totalRequests).toBe(9);
        });

        test('should handle empty state statistics', () => {
            const stats = rateLimiter.getStats();
            expect(stats.totalKeys).toBe(0);
            expect(stats.totalRequests).toBe(0);
        });
    });

    describe('Edge Cases', () => {
        test('should handle malformed keys gracefully', () => {
            const malformedKeys = [
                'user123',
                'user123:guild456',
            ];
            
            malformedKeys.forEach(key => {
                expect(() => rateLimiter.isRateLimited(key)).not.toThrow();
                expect(() => rateLimiter.addRequest(key)).not.toThrow();
                expect(() => rateLimiter.getRemainingTime(key)).not.toThrow();
            });
        });

        test('should handle concurrent access', async () => {
            const key = 'user123:guild456:ping';
            
            const promises = Array(20).fill().map(() => 
                Promise.resolve().then(() => {
                    rateLimiter.addRequest(key);
                    return rateLimiter.isRateLimited(key);
                })
            );
            
            const results = await Promise.all(promises);
            
            // Should eventually return true (rate limited)
            expect(results.some(result => result === true)).toBe(true);
        });

        test('should handle very large time advances', () => {
            const key = 'user123:guild456:ping';
            const originalNow = Date.now;
            let mockTime = Date.now();
            
            Date.now = jest.fn(() => mockTime);
            
            rateLimiter.addRequest(key);
            
            // Advance time by a very large amount
            mockTime += 1000000000; // ~11.5 days
            
            expect(rateLimiter.isRateLimited(key)).toBe(false);
            expect(rateLimiter.getRemainingTime(key)).toBe(0);
            
            Date.now = originalNow;
        });
    });
});
