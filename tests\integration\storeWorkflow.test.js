/**
 * Integration tests for store creation and management workflow
 */

describe('Store Management Workflow Integration', () => {
    let mockClient;
    let mockInteraction;
    let mockUser;
    let mockStore;
    let mockProduct;
    let storeWorkflow;

    beforeAll(async () => {
        // Mock models
        mockUser = {
            discordId: '123456789',
            username: 'TestUser',
            balance: 1000,
            save: jest.fn().mockResolvedValue(true),
            deductBalance: jest.fn().mockImplementation(function(amount) {
                this.balance -= amount;
                return this.save();
            })
        };

        mockStore = {
            _id: '507f1f77bcf86cd799439012',
            name: 'Test Store',
            description: 'A test store',
            guildId: '987654321',
            channelId: '555666777',
            ownerId: '123456789',
            isActive: true,
            stats: {
                totalProducts: 0,
                totalSales: 0,
                totalRevenue: 0
            },
            save: jest.fn().mockResolvedValue(true),
            addProduct: jest.fn().mockImplementation(function() {
                this.stats.totalProducts += 1;
                return this.save();
            })
        };

        mockProduct = {
            _id: '507f1f77bcf86cd799439011',
            name: 'Test Product',
            description: 'A test product',
            price: 29.99,
            storeId: mockStore._id,
            ownerId: '123456789',
            stockQuantity: 10,
            isActive: true,
            save: jest.fn().mockResolvedValue(true),
            addPurchase: jest.fn().mockImplementation(function(quantity) {
                this.stockQuantity -= quantity;
                return this.save();
            })
        };

        // Mock client and interaction
        mockClient = {
            commands: new Map(),
            user: { tag: 'TestBot#1234' }
        };

        mockInteraction = {
            user: { 
                id: '123456789',
                tag: 'TestUser#1234',
                displayAvatarURL: () => 'https://example.com/avatar.png'
            },
            guild: { 
                id: '987654321',
                name: 'Test Guild',
                channels: {
                    cache: new Map()
                }
            },
            channel: {
                id: '555666777',
                name: 'test-channel'
            },
            client: mockClient,
            reply: jest.fn().mockResolvedValue(true),
            followUp: jest.fn().mockResolvedValue(true),
            editReply: jest.fn().mockResolvedValue(true),
            showModal: jest.fn().mockResolvedValue(true),
            replied: false,
            deferred: false
        };

        // Mock store workflow
        storeWorkflow = {
            async createStore(interaction, storeData) {
                // Validate user permissions
                if (!interaction.member?.permissions?.has('MANAGE_CHANNELS')) {
                    throw new Error('Permissões insuficientes');
                }

                // Check if user already has a store in this guild
                const existingStore = await this.findUserStore(interaction.user.id, interaction.guild.id);
                if (existingStore) {
                    throw new Error('Usuário já possui uma loja neste servidor');
                }

                // Create store
                const store = {
                    ...mockStore,
                    ...storeData,
                    ownerId: interaction.user.id,
                    guildId: interaction.guild.id,
                    channelId: interaction.channel.id
                };
                await store.save();

                return store;
            },

            async findUserStore(userId, guildId) {
                // Mock finding existing store
                return null; // No existing store for tests
            },

            async createProduct(interaction, productData) {
                // Validate store ownership
                const store = await this.findUserStore(interaction.user.id, interaction.guild.id);
                if (!store) {
                    throw new Error('Usuário não possui uma loja');
                }

                // Create product with unique ID
                const product = { 
                    ...mockProduct, 
                    ...productData, 
                    _id: new Date().getTime().toString() + Math.random().toString(36).substr(2, 9),
                    storeId: store._id 
                };
                await product.save();

                // Update store stats
                await store.addProduct();

                return product;
            },

            async purchaseProduct(interaction, productId, quantity) {
                // Find product
                const product = mockProduct;
                if (!product.isActive) {
                    throw new Error('Produto não está ativo');
                }

                if (product.stockQuantity < quantity) {
                    throw new Error('Estoque insuficiente');
                }

                // Find user
                const user = mockUser;
                const totalCost = product.price * quantity;

                if (user.balance < totalCost) {
                    throw new Error('Saldo insuficiente');
                }

                // Process purchase
                await user.deductBalance(totalCost);
                await product.addPurchase(quantity);

                // Update store stats
                const store = mockStore;
                store.stats.totalSales += 1;
                store.stats.totalRevenue += totalCost;
                await store.save();

                return {
                    user,
                    product,
                    quantity,
                    totalCost
                };
            },

            async getStoreStats(storeId) {
                return {
                    totalProducts: mockStore.stats.totalProducts,
                    totalSales: mockStore.stats.totalSales,
                    totalRevenue: mockStore.stats.totalRevenue,
                    activeProducts: mockStore.stats.totalProducts
                };
            }
        };
    });

    beforeEach(() => {
        jest.clearAllMocks();
        
        // Reset mock data
        mockUser.balance = 1000;
        mockStore.stats = {
            totalProducts: 0,
            totalSales: 0,
            totalRevenue: 0
        };
        mockProduct.stockQuantity = 10;
        mockProduct.isActive = true;
        
        mockInteraction.replied = false;
        mockInteraction.deferred = false;
    });

    describe('Store Creation Workflow', () => {
        test('should create store successfully with valid data', async () => {
            // Mock user permissions
            mockInteraction.member = {
                permissions: {
                    has: jest.fn().mockReturnValue(true)
                }
            };

            const storeData = {
                name: 'My Test Store',
                description: 'A wonderful test store',
                color: '#FF5733'
            };

            const result = await storeWorkflow.createStore(mockInteraction, storeData);

            expect(result).toBeDefined();
            expect(result.name).toBe(storeData.name);
            expect(result.description).toBe(storeData.description);
            expect(result.ownerId).toBe(mockInteraction.user.id);
            expect(result.guildId).toBe(mockInteraction.guild.id);
            expect(mockStore.save).toHaveBeenCalled();
        });

        test('should fail store creation without permissions', async () => {
            mockInteraction.member = {
                permissions: {
                    has: jest.fn().mockReturnValue(false)
                }
            };

            const storeData = {
                name: 'My Test Store',
                description: 'A wonderful test store'
            };

            await expect(storeWorkflow.createStore(mockInteraction, storeData))
                .rejects.toThrow('Permissões insuficientes');
        });

        test('should fail store creation if user already has store', async () => {
            mockInteraction.member = {
                permissions: {
                    has: jest.fn().mockReturnValue(true)
                }
            };

            // Mock existing store
            storeWorkflow.findUserStore = jest.fn().mockResolvedValue(mockStore);

            const storeData = {
                name: 'My Second Store',
                description: 'Another store'
            };

            await expect(storeWorkflow.createStore(mockInteraction, storeData))
                .rejects.toThrow('Usuário já possui uma loja neste servidor');
        });
    });

    describe('Product Creation Workflow', () => {
        test('should create product successfully', async () => {
            // Mock user has store
            storeWorkflow.findUserStore = jest.fn().mockResolvedValue(mockStore);

            const productData = {
                name: 'Amazing Product',
                description: 'An amazing test product',
                price: 49.99,
                stockQuantity: 20
            };

            const result = await storeWorkflow.createProduct(mockInteraction, productData);

            expect(result).toBeDefined();
            expect(result.name).toBe(productData.name);
            expect(result.price).toBe(productData.price);
            expect(result.storeId).toBe(mockStore._id);
            expect(mockProduct.save).toHaveBeenCalled();
            expect(mockStore.addProduct).toHaveBeenCalled();
        });

        test('should fail product creation without store', async () => {
            // Mock user has no store
            storeWorkflow.findUserStore = jest.fn().mockResolvedValue(null);

            const productData = {
                name: 'Amazing Product',
                description: 'An amazing test product',
                price: 49.99
            };

            await expect(storeWorkflow.createProduct(mockInteraction, productData))
                .rejects.toThrow('Usuário não possui uma loja');
        });
    });

    describe('Purchase Workflow', () => {
        test('should complete purchase successfully', async () => {
            const quantity = 2;
            const expectedCost = mockProduct.price * quantity;

            const result = await storeWorkflow.purchaseProduct(mockInteraction, 'product123', quantity);

            expect(result).toBeDefined();
            expect(result.quantity).toBe(quantity);
            expect(result.totalCost).toBe(expectedCost);
            expect(mockUser.balance).toBe(1000 - expectedCost);
            expect(mockProduct.stockQuantity).toBe(10 - quantity);
            expect(mockStore.stats.totalSales).toBe(1);
            expect(mockStore.stats.totalRevenue).toBe(expectedCost);
        });

        test('should fail purchase with insufficient balance', async () => {
            mockUser.balance = 10; // Less than product price
            const quantity = 1;

            await expect(storeWorkflow.purchaseProduct(mockInteraction, 'product123', quantity))
                .rejects.toThrow('Saldo insuficiente');
        });

        test('should fail purchase with insufficient stock', async () => {
            mockProduct.stockQuantity = 1;
            const quantity = 5; // More than available stock

            await expect(storeWorkflow.purchaseProduct(mockInteraction, 'product123', quantity))
                .rejects.toThrow('Estoque insuficiente');
        });

        test('should fail purchase of inactive product', async () => {
            mockProduct.isActive = false;
            const quantity = 1;

            await expect(storeWorkflow.purchaseProduct(mockInteraction, 'product123', quantity))
                .rejects.toThrow('Produto não está ativo');
        });
    });

    describe('Complete Store Management Workflow', () => {
        test('should handle complete store lifecycle', async () => {
            // Step 1: Create store
            mockInteraction.member = {
                permissions: {
                    has: jest.fn().mockReturnValue(true)
                }
            };

            const storeData = {
                name: 'Complete Test Store',
                description: 'A complete test store'
            };

            const store = await storeWorkflow.createStore(mockInteraction, storeData);
            expect(store).toBeDefined();

            // Step 2: Create products
            storeWorkflow.findUserStore = jest.fn().mockResolvedValue(store);

            const product1Data = {
                name: 'Product 1',
                price: 25.00,
                stockQuantity: 5
            };

            const product2Data = {
                name: 'Product 2',
                price: 50.00,
                stockQuantity: 3
            };

            const product1 = await storeWorkflow.createProduct(mockInteraction, product1Data);
            const product2 = await storeWorkflow.createProduct(mockInteraction, product2Data);

            expect(product1).toBeDefined();
            expect(product2).toBeDefined();
            expect(store.stats.totalProducts).toBe(2);

            // Step 3: Make purchases
            mockProduct.price = 25.00;
            mockProduct.stockQuantity = 5;
            
            const purchase1 = await storeWorkflow.purchaseProduct(mockInteraction, 'product1', 2);
            expect(purchase1.totalCost).toBe(50.00);
            expect(mockUser.balance).toBe(950.00);

            mockProduct.price = 50.00;
            mockProduct.stockQuantity = 3;
            
            const purchase2 = await storeWorkflow.purchaseProduct(mockInteraction, 'product2', 1);
            expect(purchase2.totalCost).toBe(50.00);
            expect(mockUser.balance).toBe(900.00);

            // Step 4: Check store stats
            const stats = await storeWorkflow.getStoreStats(store._id);
            expect(stats.totalSales).toBe(2);
            expect(stats.totalRevenue).toBe(100.00);
        });

        test('should handle multiple users and stores', async () => {
            // Reset findUserStore to always return null for this test
            storeWorkflow.findUserStore = jest.fn().mockResolvedValue(null);

            // User 1 creates store
            const user1Interaction = {
                ...mockInteraction,
                user: { id: 'user1', tag: 'User1#1111' },
                member: { permissions: { has: jest.fn().mockReturnValue(true) } }
            };

            const store1 = await storeWorkflow.createStore(user1Interaction, {
                name: 'Store 1',
                description: 'First store'
            });

            // User 2 creates store
            const user2Interaction = {
                ...mockInteraction,
                user: { id: 'user2', tag: 'User2#2222' },
                member: { permissions: { has: jest.fn().mockReturnValue(true) } }
            };

            const store2 = await storeWorkflow.createStore(user2Interaction, {
                name: 'Store 2',
                description: 'Second store'
            });

            expect(store1.ownerId).toBe(user1Interaction.user.id);
            expect(store2.ownerId).toBe(user2Interaction.user.id);
            expect(store1.name).toBe('Store 1');
            expect(store2.name).toBe('Store 2');
        });

        test('should handle concurrent operations', async () => {
            const quantity = 1;
            const initialStock = mockProduct.stockQuantity;
            const initialBalance = mockUser.balance;

            // Simulate concurrent purchases
            const purchases = Array(3).fill().map(() => 
                storeWorkflow.purchaseProduct(mockInteraction, 'product123', quantity)
            );

            const results = await Promise.all(purchases);

            expect(results).toHaveLength(3);
            expect(mockProduct.stockQuantity).toBe(initialStock - (quantity * 3));
            expect(mockUser.balance).toBe(initialBalance - (mockProduct.price * quantity * 3));
        });
    });

    describe('Error Recovery and Edge Cases', () => {
        test('should handle database save failures', async () => {
            // Reset findUserStore to return null (no existing store)
            storeWorkflow.findUserStore = jest.fn().mockResolvedValue(null);

            mockStore.save = jest.fn().mockRejectedValue(new Error('Database error'));

            mockInteraction.member = {
                permissions: {
                    has: jest.fn().mockReturnValue(true)
                }
            };

            await expect(storeWorkflow.createStore(mockInteraction, {
                name: 'Test Store',
                description: 'Test'
            })).rejects.toThrow('Database error');
        });

        test('should handle invalid product data', async () => {
            storeWorkflow.findUserStore = jest.fn().mockResolvedValue(mockStore);

            const invalidProductData = {
                name: '', // Empty name
                price: -10, // Negative price
                stockQuantity: -5 // Negative stock
            };

            // This would typically be caught by model validation
            expect(invalidProductData.name).toBe('');
            expect(invalidProductData.price).toBeLessThan(0);
            expect(invalidProductData.stockQuantity).toBeLessThan(0);
        });

        test('should handle network timeouts gracefully', async () => {
            const timeoutError = new Error('Network timeout');
            timeoutError.code = 'TIMEOUT';

            mockProduct.save = jest.fn().mockRejectedValue(timeoutError);
            storeWorkflow.findUserStore = jest.fn().mockResolvedValue(mockStore);

            await expect(storeWorkflow.createProduct(mockInteraction, {
                name: 'Test Product',
                price: 29.99
            })).rejects.toThrow('Network timeout');
        });

        test('should maintain data consistency during failures', async () => {
            const originalBalance = mockUser.balance;
            const originalStock = mockProduct.stockQuantity;

            // Mock failure after balance deduction but before stock update
            mockProduct.addPurchase = jest.fn().mockRejectedValue(new Error('Stock update failed'));

            await expect(storeWorkflow.purchaseProduct(mockInteraction, 'product123', 1))
                .rejects.toThrow('Stock update failed');

            // In a real implementation, this would require transaction rollback
            // For this test, we just verify the error was thrown
            expect(mockProduct.addPurchase).toHaveBeenCalled();
        });
    });
});
