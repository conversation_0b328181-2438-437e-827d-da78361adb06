import EventEmitter from 'events';
import fs from 'fs/promises';
import path from 'path';

/**
 * Performance Monitor for Database and Cache Operations
 * 
 * Tracks:
 * - Database query performance
 * - Cache hit/miss rates
 * - Memory usage
 * - Response times
 * - Error rates
 */

class PerformanceMonitor extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            enableLogging: options.enableLogging ?? true,
            logInterval: options.logInterval ?? 60000, // 1 minute
            maxMetricsHistory: options.maxMetricsHistory ?? 1000,
            slowQueryThreshold: options.slowQueryThreshold ?? 1000, // 1 second
            logFilePath: options.logFilePath ?? './logs/performance.log',
            ...options
        };
        
        this.metrics = {
            database: {
                queries: [],
                totalQueries: 0,
                slowQueries: 0,
                averageResponseTime: 0,
                errors: 0
            },
            cache: {
                hits: 0,
                misses: 0,
                sets: 0,
                deletes: 0,
                hitRate: 0
            },
            memory: {
                heapUsed: 0,
                heapTotal: 0,
                external: 0,
                rss: 0
            },
            system: {
                uptime: 0,
                loadAverage: [0, 0, 0]
            }
        };
        
        this.startTime = Date.now();
        this.lastLogTime = Date.now();
        
        if (this.options.enableLogging) {
            this.startPeriodicLogging();
        }
        
        // Track memory usage
        this.startMemoryTracking();
    }
    
    /**
     * Track database query performance
     */
    trackDatabaseQuery(operation, collection, duration, error = null) {
        const queryData = {
            timestamp: Date.now(),
            operation,
            collection,
            duration,
            error: error ? error.message : null
        };
        
        this.metrics.database.queries.push(queryData);
        this.metrics.database.totalQueries++;
        
        if (error) {
            this.metrics.database.errors++;
        }
        
        if (duration > this.options.slowQueryThreshold) {
            this.metrics.database.slowQueries++;
            this.emit('slowQuery', queryData);
        }
        
        // Calculate average response time
        const recentQueries = this.metrics.database.queries.slice(-100);
        this.metrics.database.averageResponseTime = 
            recentQueries.reduce((sum, q) => sum + q.duration, 0) / recentQueries.length;
        
        // Limit history size
        if (this.metrics.database.queries.length > this.options.maxMetricsHistory) {
            this.metrics.database.queries = this.metrics.database.queries.slice(-this.options.maxMetricsHistory);
        }
        
        this.emit('databaseQuery', queryData);
    }
    
    /**
     * Track cache operations
     */
    trackCacheHit(key, type = 'get') {
        this.metrics.cache.hits++;
        this.updateCacheHitRate();
        this.emit('cacheHit', { key, type, timestamp: Date.now() });
    }
    
    trackCacheMiss(key, type = 'get') {
        this.metrics.cache.misses++;
        this.updateCacheHitRate();
        this.emit('cacheMiss', { key, type, timestamp: Date.now() });
    }
    
    trackCacheSet(key, ttl = null) {
        this.metrics.cache.sets++;
        this.emit('cacheSet', { key, ttl, timestamp: Date.now() });
    }
    
    trackCacheDelete(key) {
        this.metrics.cache.deletes++;
        this.emit('cacheDelete', { key, timestamp: Date.now() });
    }
    
    updateCacheHitRate() {
        const total = this.metrics.cache.hits + this.metrics.cache.misses;
        this.metrics.cache.hitRate = total > 0 ? (this.metrics.cache.hits / total) * 100 : 0;
    }
    
    /**
     * Start memory usage tracking
     */
    startMemoryTracking() {
        const updateMemoryMetrics = () => {
            const memUsage = process.memoryUsage();
            this.metrics.memory = {
                heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
                heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
                external: Math.round(memUsage.external / 1024 / 1024), // MB
                rss: Math.round(memUsage.rss / 1024 / 1024) // MB
            };
            
            this.metrics.system.uptime = Math.round(process.uptime());
            
            // Check for memory leaks
            if (this.metrics.memory.heapUsed > 500) { // 500MB threshold
                this.emit('highMemoryUsage', this.metrics.memory);
            }
        };
        
        updateMemoryMetrics();
        setInterval(updateMemoryMetrics, 30000); // Every 30 seconds
    }
    
    /**
     * Start periodic logging
     */
    startPeriodicLogging() {
        setInterval(async () => {
            await this.logMetrics();
        }, this.options.logInterval);
    }
    
    /**
     * Get current performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            uptime: Date.now() - this.startTime,
            timestamp: Date.now()
        };
    }
    
    /**
     * Get database performance summary
     */
    getDatabaseSummary(timeWindow = 3600000) { // 1 hour default
        const cutoff = Date.now() - timeWindow;
        const recentQueries = this.metrics.database.queries.filter(q => q.timestamp > cutoff);
        
        if (recentQueries.length === 0) {
            return {
                totalQueries: 0,
                averageResponseTime: 0,
                slowQueries: 0,
                errorRate: 0,
                operationBreakdown: {},
                collectionBreakdown: {}
            };
        }
        
        const operationBreakdown = {};
        const collectionBreakdown = {};
        let totalDuration = 0;
        let slowQueries = 0;
        let errors = 0;
        
        recentQueries.forEach(query => {
            // Operation breakdown
            operationBreakdown[query.operation] = (operationBreakdown[query.operation] || 0) + 1;
            
            // Collection breakdown
            collectionBreakdown[query.collection] = (collectionBreakdown[query.collection] || 0) + 1;
            
            totalDuration += query.duration;
            
            if (query.duration > this.options.slowQueryThreshold) {
                slowQueries++;
            }
            
            if (query.error) {
                errors++;
            }
        });
        
        return {
            totalQueries: recentQueries.length,
            averageResponseTime: Math.round(totalDuration / recentQueries.length),
            slowQueries,
            errorRate: Math.round((errors / recentQueries.length) * 100),
            operationBreakdown,
            collectionBreakdown
        };
    }
    
    /**
     * Get cache performance summary
     */
    getCacheSummary() {
        return {
            ...this.metrics.cache,
            totalOperations: this.metrics.cache.hits + this.metrics.cache.misses + this.metrics.cache.sets + this.metrics.cache.deletes
        };
    }
    
    /**
     * Get slow queries
     */
    getSlowQueries(limit = 10) {
        return this.metrics.database.queries
            .filter(q => q.duration > this.options.slowQueryThreshold)
            .sort((a, b) => b.duration - a.duration)
            .slice(0, limit);
    }
    
    /**
     * Get performance alerts
     */
    getAlerts() {
        const alerts = [];
        
        // High error rate
        const recentQueries = this.metrics.database.queries.slice(-100);
        const errorRate = recentQueries.filter(q => q.error).length / recentQueries.length;
        if (errorRate > 0.05) { // 5% error rate
            alerts.push({
                type: 'high_error_rate',
                severity: 'warning',
                message: `Database error rate is ${Math.round(errorRate * 100)}%`,
                value: errorRate
            });
        }
        
        // Low cache hit rate
        if (this.metrics.cache.hitRate < 70 && (this.metrics.cache.hits + this.metrics.cache.misses) > 100) {
            alerts.push({
                type: 'low_cache_hit_rate',
                severity: 'warning',
                message: `Cache hit rate is ${Math.round(this.metrics.cache.hitRate)}%`,
                value: this.metrics.cache.hitRate
            });
        }
        
        // High memory usage
        if (this.metrics.memory.heapUsed > 400) { // 400MB
            alerts.push({
                type: 'high_memory_usage',
                severity: 'warning',
                message: `Memory usage is ${this.metrics.memory.heapUsed}MB`,
                value: this.metrics.memory.heapUsed
            });
        }
        
        // Too many slow queries
        const slowQueryRate = this.metrics.database.slowQueries / this.metrics.database.totalQueries;
        if (slowQueryRate > 0.1 && this.metrics.database.totalQueries > 50) { // 10% slow queries
            alerts.push({
                type: 'high_slow_query_rate',
                severity: 'critical',
                message: `${Math.round(slowQueryRate * 100)}% of queries are slow`,
                value: slowQueryRate
            });
        }
        
        return alerts;
    }
    
    /**
     * Log metrics to file
     */
    async logMetrics() {
        try {
            const metrics = this.getMetrics();
            const summary = this.getDatabaseSummary();
            const cacheSummary = this.getCacheSummary();
            const alerts = this.getAlerts();
            
            const logEntry = {
                timestamp: new Date().toISOString(),
                uptime: metrics.uptime,
                database: summary,
                cache: cacheSummary,
                memory: metrics.memory,
                alerts
            };
            
            // Ensure log directory exists
            const logDir = path.dirname(this.options.logFilePath);
            await fs.mkdir(logDir, { recursive: true });
            
            // Append to log file
            await fs.appendFile(this.options.logFilePath, JSON.stringify(logEntry) + '\n');
            
            // Emit log event
            this.emit('metricsLogged', logEntry);
            
        } catch (error) {
            console.error('Failed to log performance metrics:', error);
        }
    }
    
    /**
     * Reset metrics
     */
    reset() {
        this.metrics = {
            database: {
                queries: [],
                totalQueries: 0,
                slowQueries: 0,
                averageResponseTime: 0,
                errors: 0
            },
            cache: {
                hits: 0,
                misses: 0,
                sets: 0,
                deletes: 0,
                hitRate: 0
            },
            memory: {
                heapUsed: 0,
                heapTotal: 0,
                external: 0,
                rss: 0
            },
            system: {
                uptime: 0,
                loadAverage: [0, 0, 0]
            }
        };
        
        this.startTime = Date.now();
        this.emit('metricsReset');
    }
    
    /**
     * Generate performance report
     */
    generateReport(timeWindow = 3600000) { // 1 hour
        const metrics = this.getMetrics();
        const dbSummary = this.getDatabaseSummary(timeWindow);
        const cacheSummary = this.getCacheSummary();
        const slowQueries = this.getSlowQueries();
        const alerts = this.getAlerts();
        
        return {
            generatedAt: new Date().toISOString(),
            timeWindow: timeWindow,
            uptime: metrics.uptime,
            database: {
                ...dbSummary,
                slowQueries: slowQueries.map(q => ({
                    operation: q.operation,
                    collection: q.collection,
                    duration: q.duration,
                    timestamp: new Date(q.timestamp).toISOString()
                }))
            },
            cache: cacheSummary,
            memory: metrics.memory,
            alerts,
            recommendations: this.generateRecommendations(dbSummary, cacheSummary, alerts)
        };
    }
    
    /**
     * Generate performance recommendations
     */
    generateRecommendations(dbSummary, cacheSummary, alerts) {
        const recommendations = [];
        
        // Database recommendations
        if (dbSummary.averageResponseTime > 500) {
            recommendations.push({
                type: 'database',
                priority: 'high',
                message: 'Consider adding indexes for frequently queried fields',
                reason: `Average response time is ${dbSummary.averageResponseTime}ms`
            });
        }
        
        if (dbSummary.slowQueries > 10) {
            recommendations.push({
                type: 'database',
                priority: 'medium',
                message: 'Review and optimize slow queries',
                reason: `${dbSummary.slowQueries} slow queries detected`
            });
        }
        
        // Cache recommendations
        if (cacheSummary.hitRate < 80 && cacheSummary.totalOperations > 100) {
            recommendations.push({
                type: 'cache',
                priority: 'medium',
                message: 'Consider increasing cache TTL or caching more data',
                reason: `Cache hit rate is only ${Math.round(cacheSummary.hitRate)}%`
            });
        }
        
        // Memory recommendations
        if (this.metrics.memory.heapUsed > 300) {
            recommendations.push({
                type: 'memory',
                priority: 'medium',
                message: 'Monitor memory usage and consider optimization',
                reason: `Memory usage is ${this.metrics.memory.heapUsed}MB`
            });
        }
        
        return recommendations;
    }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Middleware for automatic database query tracking
export const trackDatabaseOperation = (operation, collection) => {
    const startTime = Date.now();
    
    return {
        finish: (error = null) => {
            const duration = Date.now() - startTime;
            performanceMonitor.trackDatabaseQuery(operation, collection, duration, error);
        }
    };
};

// Middleware for cache operations
export const cacheMiddleware = {
    hit: (key, type) => performanceMonitor.trackCacheHit(key, type),
    miss: (key, type) => performanceMonitor.trackCacheMiss(key, type),
    set: (key, ttl) => performanceMonitor.trackCacheSet(key, ttl),
    delete: (key) => performanceMonitor.trackCacheDelete(key)
};

export default performanceMonitor;