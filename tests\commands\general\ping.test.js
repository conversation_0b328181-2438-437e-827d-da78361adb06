/**
 * Tests for ping command
 */

describe('Ping Command', () => {
    let pingCommand;
    let mockInteraction;

    beforeAll(async () => {
        // Mock interaction
        mockInteraction = {
            client: {
                ws: { ping: 50 },
                user: { tag: 'TestBot#1234' }
            },
            reply: jest.fn().mockResolvedValue(true),
            user: {
                id: '123456789',
                tag: 'TestUser#1234',
                displayAvatarURL: jest.fn().mockReturnValue('https://example.com/avatar.png')
            },
            guild: { id: '987654321' },
            createdTimestamp: Date.now()
        };

        // Mock the ping command
        pingCommand = {
            data: {
                name: 'ping',
                description: 'Responde com Pong! e mostra a latência do bot'
            },
            async execute(interaction) {
                const latency = Date.now() - interaction.createdTimestamp;
                const apiLatency = Math.round(interaction.client.ws.ping);
                
                const embed = {
                    color: 0x00FF00,
                    title: '🏓 Pong!',
                    fields: [
                        {
                            name: 'Latência do Bot',
                            value: `${latency}ms`,
                            inline: true
                        },
                        {
                            name: 'Latência da API',
                            value: `${apiLatency}ms`,
                            inline: true
                        }
                    ],
                    timestamp: new Date().toISOString(),
                    footer: {
                        text: `Solicitado por ${interaction.user.tag}`,
                        icon_url: interaction.user.displayAvatarURL()
                    }
                };

                await interaction.reply({ embeds: [embed] });
            }
        };
    });

    describe('Command Structure', () => {
        test('should have correct command data', () => {
            expect(pingCommand.data).toBeDefined();
            expect(pingCommand.data.name).toBe('ping');
            expect(pingCommand.data.description).toBe('Responde com Pong! e mostra a latência do bot');
        });

        test('should have execute function', () => {
            expect(pingCommand.execute).toBeDefined();
            expect(typeof pingCommand.execute).toBe('function');
        });
    });

    describe('Command Execution', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        test('should reply with pong embed', async () => {
            await pingCommand.execute(mockInteraction);

            expect(mockInteraction.reply).toHaveBeenCalledTimes(1);
            
            const replyCall = mockInteraction.reply.mock.calls[0][0];
            expect(replyCall).toHaveProperty('embeds');
            expect(replyCall.embeds).toHaveLength(1);
            
            const embed = replyCall.embeds[0];
            expect(embed.title).toBe('🏓 Pong!');
            expect(embed.color).toBe(0x00FF00);
        });

        test('should include bot latency in embed', async () => {
            const startTime = Date.now();
            mockInteraction.createdTimestamp = startTime - 100; // 100ms ago
            
            await pingCommand.execute(mockInteraction);

            const replyCall = mockInteraction.reply.mock.calls[0][0];
            const embed = replyCall.embeds[0];
            
            const latencyField = embed.fields.find(field => field.name === 'Latência do Bot');
            expect(latencyField).toBeDefined();
            expect(latencyField.value).toMatch(/\d+ms/);
            expect(latencyField.inline).toBe(true);
        });

        test('should include API latency in embed', async () => {
            await pingCommand.execute(mockInteraction);

            const replyCall = mockInteraction.reply.mock.calls[0][0];
            const embed = replyCall.embeds[0];
            
            const apiLatencyField = embed.fields.find(field => field.name === 'Latência da API');
            expect(apiLatencyField).toBeDefined();
            expect(apiLatencyField.value).toBe('50ms');
            expect(apiLatencyField.inline).toBe(true);
        });

        test('should include timestamp in embed', async () => {
            await pingCommand.execute(mockInteraction);

            const replyCall = mockInteraction.reply.mock.calls[0][0];
            const embed = replyCall.embeds[0];
            
            expect(embed.timestamp).toBeDefined();
            expect(new Date(embed.timestamp)).toBeInstanceOf(Date);
        });

        test('should include footer with user info', async () => {
            mockInteraction.user.tag = 'TestUser#1234';
            mockInteraction.user.displayAvatarURL = jest.fn().mockReturnValue('https://example.com/avatar.png');
            
            await pingCommand.execute(mockInteraction);

            const replyCall = mockInteraction.reply.mock.calls[0][0];
            const embed = replyCall.embeds[0];
            
            expect(embed.footer).toBeDefined();
            expect(embed.footer.text).toBe('Solicitado por TestUser#1234');
            expect(embed.footer.icon_url).toBe('https://example.com/avatar.png');
        });

        test('should handle high latency values', async () => {
            mockInteraction.client.ws.ping = 500;
            mockInteraction.createdTimestamp = Date.now() - 1000; // 1000ms ago
            
            await pingCommand.execute(mockInteraction);

            const replyCall = mockInteraction.reply.mock.calls[0][0];
            const embed = replyCall.embeds[0];
            
            const botLatencyField = embed.fields.find(field => field.name === 'Latência do Bot');
            const apiLatencyField = embed.fields.find(field => field.name === 'Latência da API');
            
            expect(parseInt(botLatencyField.value)).toBeGreaterThan(900);
            expect(apiLatencyField.value).toBe('500ms');
        });

        test('should handle zero latency values', async () => {
            mockInteraction.client.ws.ping = 0;
            mockInteraction.createdTimestamp = Date.now();
            
            await pingCommand.execute(mockInteraction);

            const replyCall = mockInteraction.reply.mock.calls[0][0];
            const embed = replyCall.embeds[0];
            
            const apiLatencyField = embed.fields.find(field => field.name === 'Latência da API');
            expect(apiLatencyField.value).toBe('0ms');
        });

        test('should handle negative latency calculation', async () => {
            // Edge case where createdTimestamp is in the future
            mockInteraction.createdTimestamp = Date.now() + 1000;
            
            await pingCommand.execute(mockInteraction);

            const replyCall = mockInteraction.reply.mock.calls[0][0];
            const embed = replyCall.embeds[0];
            
            const botLatencyField = embed.fields.find(field => field.name === 'Latência do Bot');
            expect(botLatencyField.value).toMatch(/-?\d+ms/);
        });
    });

    describe('Error Handling', () => {
        test('should handle reply failure', async () => {
            // Create a separate interaction for this test
            const failingInteraction = {
                ...mockInteraction,
                reply: jest.fn().mockRejectedValue(new Error('Reply failed'))
            };

            try {
                await pingCommand.execute(failingInteraction);
                fail('Should have thrown an error');
            } catch (error) {
                expect(error.message).toBe('Reply failed');
            }
        });

        test('should handle missing client data', async () => {
            const incompleteInteraction = {
                ...mockInteraction,
                client: { user: { tag: 'TestBot#1234' } }, // Missing ws.ping
                reply: jest.fn().mockResolvedValue(true)
            };

            // Should handle missing ws.ping gracefully
            try {
                await pingCommand.execute(incompleteInteraction);
                expect(incompleteInteraction.reply).toHaveBeenCalled();
            } catch (error) {
                // Expected to fail due to missing ws.ping, but should not crash
                expect(error).toBeDefined();
            }
        });

        test('should handle missing user data', async () => {
            const incompleteInteraction = {
                ...mockInteraction,
                user: { tag: 'TestUser#1234' }, // Missing displayAvatarURL
                reply: jest.fn().mockResolvedValue(true)
            };

            try {
                await pingCommand.execute(incompleteInteraction);
                expect(incompleteInteraction.reply).toHaveBeenCalled();
            } catch (error) {
                // Expected to fail due to missing displayAvatarURL, but should not crash
                expect(error).toBeDefined();
            }
        });

        test('should handle missing displayAvatarURL method', async () => {
            const incompleteInteraction = {
                ...mockInteraction,
                user: {
                    id: '123456789',
                    tag: 'TestUser#1234'
                    // missing displayAvatarURL method
                }
            };
            
            await expect(pingCommand.execute(incompleteInteraction)).rejects.toThrow();
        });
    });

    describe('Performance', () => {
        test('should execute quickly', async () => {
            const startTime = Date.now();
            
            await pingCommand.execute(mockInteraction);
            
            const executionTime = Date.now() - startTime;
            expect(executionTime).toBeLessThan(100); // Should execute in less than 100ms
        });

        test('should handle multiple concurrent executions', async () => {
            const promises = Array(10).fill().map(() => 
                pingCommand.execute({
                    ...mockInteraction,
                    reply: jest.fn().mockResolvedValue(true)
                })
            );
            
            await expect(Promise.all(promises)).resolves.not.toThrow();
        });
    });

    describe('Integration', () => {
        test('should work with different client ping values', async () => {
            const pingValues = [0, 25, 50, 100, 250, 500, 1000];
            
            for (const ping of pingValues) {
                mockInteraction.client.ws.ping = ping;
                mockInteraction.reply = jest.fn().mockResolvedValue(true);
                
                await pingCommand.execute(mockInteraction);
                
                const replyCall = mockInteraction.reply.mock.calls[0][0];
                const embed = replyCall.embeds[0];
                const apiLatencyField = embed.fields.find(field => field.name === 'Latência da API');
                
                expect(apiLatencyField.value).toBe(`${ping}ms`);
            }
        });

        test('should work with different user tags', async () => {
            const userTags = ['User#0001', 'TestBot#1234', 'Admin#9999'];
            
            for (const tag of userTags) {
                mockInteraction.user.tag = tag;
                mockInteraction.reply = jest.fn().mockResolvedValue(true);
                
                await pingCommand.execute(mockInteraction);
                
                const replyCall = mockInteraction.reply.mock.calls[0][0];
                const embed = replyCall.embeds[0];
                
                expect(embed.footer.text).toBe(`Solicitado por ${tag}`);
            }
        });

        test('should maintain embed structure consistency', async () => {
            await pingCommand.execute(mockInteraction);

            const replyCall = mockInteraction.reply.mock.calls[0][0];
            const embed = replyCall.embeds[0];
            
            // Check all required embed properties
            expect(embed).toHaveProperty('color');
            expect(embed).toHaveProperty('title');
            expect(embed).toHaveProperty('fields');
            expect(embed).toHaveProperty('timestamp');
            expect(embed).toHaveProperty('footer');
            
            // Check fields structure
            expect(embed.fields).toHaveLength(2);
            embed.fields.forEach(field => {
                expect(field).toHaveProperty('name');
                expect(field).toHaveProperty('value');
                expect(field).toHaveProperty('inline');
            });
        });
    });
});
