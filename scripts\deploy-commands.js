import { REST, Routes } from 'discord.js';
import { config } from 'dotenv';
import { readdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carrega variáveis de ambiente
config({ path: join(__dirname, '..', '.env') });

const commands = [];

/**
 * Carrega todos os comandos das subpastas
 */
async function loadCommands() {
    const commandsPath = join(__dirname, '..', 'src', 'commands');
    
    try {
        // Lê todas as categorias de comandos (subpastas)
        const commandCategories = readdirSync(commandsPath, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);

        console.log(`📁 Encontradas ${commandCategories.length} categorias de comandos: ${commandCategories.join(', ')}`);

        for (const category of commandCategories) {
            const categoryPath = join(commandsPath, category);
            
            // Lê todos os arquivos de comando da categoria
            const commandFiles = readdirSync(categoryPath)
                .filter(file => file.endsWith('.js'));

            console.log(`📋 Categoria ${category}: ${commandFiles.length} comando(s)`);

            for (const file of commandFiles) {
                try {
                    const filePath = join(categoryPath, file);
                    const fileUrl = `file://${filePath.replace(/\\/g, '/')}`;
                    
                    // Importa o comando dinamicamente
                    const command = await import(fileUrl);
                    
                    if (command.default && command.default.data && command.default.execute) {
                        commands.push(command.default.data.toJSON());
                        console.log(`✅ Comando carregado: ${command.default.data.name} (${category})`);
                    } else {
                        console.warn(`⚠️ Comando inválido: ${file} - faltando propriedades obrigatórias`);
                    }
                } catch (error) {
                    console.error(`❌ Erro ao carregar comando ${file}:`, error.message);
                }
            }
        }
        
        console.log(`\n📊 Total de comandos carregados: ${commands.length}`);
        
    } catch (error) {
        console.error('❌ Erro ao carregar comandos:', error);
        process.exit(1);
    }
}

/**
 * Registra os comandos no Discord
 */
async function deployCommands() {
    const { DISCORD_TOKEN, CLIENT_ID, GUILD_ID } = process.env;
    
    // Validação das variáveis de ambiente
    if (!DISCORD_TOKEN) {
        console.error('❌ DISCORD_TOKEN não encontrado no arquivo .env');
        process.exit(1);
    }
    
    if (!CLIENT_ID) {
        console.error('❌ CLIENT_ID não encontrado no arquivo .env');
        process.exit(1);
    }
    
    const rest = new REST().setToken(DISCORD_TOKEN);
    
    try {
        console.log(`\n🚀 Iniciando registro de ${commands.length} comandos slash...`);
        
        let data;
        
        if (GUILD_ID) {
            // Registra comandos apenas no servidor específico (desenvolvimento)
            console.log(`🎯 Registrando comandos no servidor: ${GUILD_ID}`);
            data = await rest.put(
                Routes.applicationGuildCommands(CLIENT_ID, GUILD_ID),
                { body: commands }
            );
        } else {
            // Registra comandos globalmente (produção)
            console.log('🌍 Registrando comandos globalmente...');
            data = await rest.put(
                Routes.applicationCommands(CLIENT_ID),
                { body: commands }
            );
        }
        
        console.log(`✅ ${data.length} comandos registrados com sucesso!`);
        
        // Lista os comandos registrados
        console.log('\n📋 Comandos registrados:');
        data.forEach((command, index) => {
            console.log(`${index + 1}. /${command.name} - ${command.description}`);
        });
        
    } catch (error) {
        console.error('❌ Erro ao registrar comandos:', error);
        
        if (error.code === 50001) {
            console.error('💡 Dica: Verifique se o bot tem as permissões necessárias no servidor.');
        } else if (error.code === 10002) {
            console.error('💡 Dica: Verifique se o CLIENT_ID está correto.');
        } else if (error.rawError?.code === 50035) {
            console.error('💡 Dica: Há um erro na estrutura de um dos comandos.');
            console.error('Detalhes:', error.rawError.errors);
        }
        
        process.exit(1);
    }
}

/**
 * Função principal
 */
async function main() {
    console.log('🤖 Discord Store Bot - Deploy de Comandos\n');
    
    await loadCommands();
    await deployCommands();
    
    console.log('\n🎉 Deploy concluído com sucesso!');
    
    if (process.env.GUILD_ID) {
        console.log('💡 Os comandos devem aparecer imediatamente no servidor especificado.');
    } else {
        console.log('💡 Os comandos globais podem levar até 1 hora para aparecer em todos os servidores.');
    }
}

// Executa o script
main().catch(console.error);
