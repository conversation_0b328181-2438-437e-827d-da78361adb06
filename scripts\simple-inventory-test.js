#!/usr/bin/env node

/**
 * Script simples para testar funcionalidades de inventário
 */

import mongoose from 'mongoose';
import Product from '../src/models/Product.js';
import Store from '../src/models/Store.js';
import StockItem from '../src/models/StockItem.js';

console.log('🚀 Iniciando testes de inventário...');

async function runTests() {
    try {
        // Conecta ao banco de dados
        console.log('📡 Conectando ao banco de dados...');
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/tarolha_test');
        console.log('✅ Conectado ao banco de dados');

        // Limpa dados de teste
        console.log('🧹 Limpando dados de teste...');
        await Store.deleteMany({ name: /Teste/ });
        await Product.deleteMany({ name: /Teste/ });
        await StockItem.deleteMany({});

        // Cria loja de teste
        console.log('🏪 Criando loja de teste...');
        const testStore = await Store.create({
            name: 'Loja Teste Inventário',
            description: 'Loja para testar inventário',
            ownerId: 'test-user-123',
            guildId: 'test-guild-456',
            isActive: true
        });
        console.log(`✅ Loja criada: ${testStore.name}`);

        // Cria produto de teste
        console.log('🛍️ Criando produto de teste...');
        const testProduct = await Product.create({
            name: 'Produto Teste Inventário',
            description: 'Produto para testar funcionalidades de inventário',
            price: 19.99,
            category: 'Teste',
            storeId: testStore._id,
            status: 'active',
            emoji: '🧪'
        });
        console.log(`✅ Produto criado: ${testProduct.name}`);

        // Cria itens de estoque
        console.log('📦 Criando itens de estoque...');
        const stockItems = [];
        
        // 15 itens disponíveis
        for (let i = 1; i <= 15; i++) {
            stockItems.push({
                productId: testProduct._id,
                content: `Item disponível ${i} - conteúdo para busca`,
                status: 'available'
            });
        }

        // 3 itens vendidos
        for (let i = 1; i <= 3; i++) {
            stockItems.push({
                productId: testProduct._id,
                content: `Item vendido ${i} - teste vendido`,
                status: 'sold',
                soldAt: new Date()
            });
        }

        // 2 itens reservados
        for (let i = 1; i <= 2; i++) {
            stockItems.push({
                productId: testProduct._id,
                content: `Item reservado ${i} - teste reservado`,
                status: 'reserved',
                reservedAt: new Date()
            });
        }

        // 1 item expirado
        stockItems.push({
            productId: testProduct._id,
            content: 'Item expirado 1 - teste expirado',
            status: 'expired'
        });

        await StockItem.create(stockItems);
        console.log(`✅ ${stockItems.length} itens de estoque criados`);

        // Testa resumo de estoque
        console.log('\n📊 Testando resumo de estoque...');
        const summary = await StockItem.getStockSummary(testProduct._id);
        console.log(`   🟢 Disponível: ${summary.available}`);
        console.log(`   🔴 Vendido: ${summary.sold}`);
        console.log(`   🟡 Reservado: ${summary.reserved}`);
        console.log(`   ⚫ Expirado: ${summary.expired}`);
        console.log(`   📈 Total: ${summary.total}`);

        // Testa busca
        console.log('\n🔍 Testando busca no estoque...');
        const searchResults = await StockItem.find({
            productId: testProduct._id,
            content: { $regex: 'disponível', $options: 'i' }
        });
        console.log(`   🔎 Busca por "disponível": ${searchResults.length} resultados`);

        const searchWithStatus = await StockItem.find({
            productId: testProduct._id,
            content: { $regex: 'teste', $options: 'i' },
            status: 'sold'
        });
        console.log(`   🔎 Busca por "teste" (vendidos): ${searchWithStatus.length} resultados`);

        // Testa paginação
        console.log('\n📄 Testando paginação...');
        const availableItems = await StockItem.find({ 
            productId: testProduct._id, 
            status: 'available' 
        }).sort({ createdAt: -1 });
        
        const itemsPerPage = 10;
        const totalPages = Math.ceil(availableItems.length / itemsPerPage);
        console.log(`   📋 Total de itens disponíveis: ${availableItems.length}`);
        console.log(`   📄 Total de páginas (${itemsPerPage} por página): ${totalPages}`);

        // Simula primeira página
        const page1 = availableItems.slice(0, itemsPerPage);
        console.log(`   📄 Página 1: ${page1.length} itens`);

        if (totalPages > 1) {
            const page2 = availableItems.slice(itemsPerPage, itemsPerPage * 2);
            console.log(`   📄 Página 2: ${page2.length} itens`);
        }

        console.log('\n✅ Todos os testes executados com sucesso!');
        console.log('\n📋 Funcionalidades testadas:');
        console.log('   ✅ Criação de dados de teste');
        console.log('   ✅ Resumo de estoque (getStockSummary)');
        console.log('   ✅ Busca por conteúdo (regex)');
        console.log('   ✅ Busca com filtro de status');
        console.log('   ✅ Paginação de resultados');
        console.log('   ✅ Contagem de itens por status');

        // Limpa dados de teste
        console.log('\n🧹 Limpando dados de teste...');
        await Store.deleteMany({ _id: testStore._id });
        await Product.deleteMany({ _id: testProduct._id });
        await StockItem.deleteMany({ productId: testProduct._id });

    } catch (error) {
        console.error('❌ Erro durante os testes:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Desconectado do banco de dados');
    }
}

runTests().catch(console.error);
