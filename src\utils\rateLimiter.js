import { logger } from './logger.js';
import BotConfig from '../models/BotConfig.js';
import { BotLogger } from './botLogger.js';

/**
 * Sistema de Rate Limiting para comandos do bot
 */
export class RateLimiter {
    constructor() {
        // Map para armazenar tentativas por usuário
        // Estrutura: userId -> { count, resetTime, guildId }
        this.attempts = new Map();
        
        // Limpa tentativas antigas a cada 5 minutos
        setInterval(() => {
            this.cleanupOldAttempts();
        }, 5 * 60 * 1000);
    }

    /**
     * Verifica se o usuário pode executar o comando
     * @param {string} userId - ID do usuário
     * @param {string} guildId - ID do servidor
     * @param {string} commandName - Nome do comando
     * @param {Client} client - Cliente do Discord
     * @returns {Promise<boolean>} - true se pode executar, false se bloqueado
     */
    async checkRateLimit(userId, guildId, commandName, client) {
        try {
            // Busca configuração do servidor
            const config = await BotConfig.findByGuild(guildId);
            
            // Se rate limiting está desabilitado, permite
            if (!config || !config.rateLimiting.isEnabled) {
                return true;
            }

            const { windowMs, maxRequests } = config.rateLimiting;
            const now = Date.now();
            const userKey = `${userId}_${guildId}`;
            
            // Busca tentativas do usuário
            let userAttempts = this.attempts.get(userKey);
            
            // Se não existe ou expirou, cria nova entrada
            if (!userAttempts || now > userAttempts.resetTime) {
                userAttempts = {
                    count: 1,
                    resetTime: now + windowMs,
                    guildId,
                    commands: [{ command: commandName, timestamp: now }]
                };
                this.attempts.set(userKey, userAttempts);
                return true;
            }
            
            // Incrementa contador
            userAttempts.count++;
            userAttempts.commands.push({ command: commandName, timestamp: now });
            
            // Verifica se excedeu o limite
            if (userAttempts.count > maxRequests) {
                // Log do rate limit atingido
                if (client) {
                    const user = await client.users.fetch(userId).catch(() => null);
                    await BotLogger.logRateLimitHit(client, guildId, user, commandName);
                }
                
                logger.warn(`Rate limit atingido para usuário ${userId} no comando ${commandName}`);
                return false;
            }
            
            return true;
            
        } catch (error) {
            logger.error('Erro ao verificar rate limit:', error);
            // Em caso de erro, permite a execução para não quebrar o bot
            return true;
        }
    }

    /**
     * Obtém informações sobre o rate limit do usuário
     * @param {string} userId - ID do usuário
     * @param {string} guildId - ID do servidor
     * @returns {Object|null} - Informações do rate limit
     */
    async getRateLimitInfo(userId, guildId) {
        try {
            const config = await BotConfig.findByGuild(guildId);
            if (!config || !config.rateLimiting.isEnabled) {
                return null;
            }

            const userKey = `${userId}_${guildId}`;
            const userAttempts = this.attempts.get(userKey);
            
            if (!userAttempts) {
                return {
                    remaining: config.rateLimiting.maxRequests,
                    resetTime: null,
                    isLimited: false
                };
            }
            
            const now = Date.now();
            const isExpired = now > userAttempts.resetTime;
            
            if (isExpired) {
                return {
                    remaining: config.rateLimiting.maxRequests,
                    resetTime: null,
                    isLimited: false
                };
            }
            
            const remaining = Math.max(0, config.rateLimiting.maxRequests - userAttempts.count);
            const isLimited = userAttempts.count >= config.rateLimiting.maxRequests;
            
            return {
                remaining,
                resetTime: userAttempts.resetTime,
                isLimited,
                windowMs: config.rateLimiting.windowMs
            };
            
        } catch (error) {
            logger.error('Erro ao obter informações de rate limit:', error);
            return null;
        }
    }

    /**
     * Reseta o rate limit de um usuário (para admins)
     * @param {string} userId - ID do usuário
     * @param {string} guildId - ID do servidor
     */
    resetUserRateLimit(userId, guildId) {
        const userKey = `${userId}_${guildId}`;
        this.attempts.delete(userKey);
        logger.info(`Rate limit resetado para usuário ${userId} no servidor ${guildId}`);
    }

    /**
     * Limpa tentativas antigas
     */
    cleanupOldAttempts() {
        const now = Date.now();
        let cleaned = 0;
        
        for (const [key, attempts] of this.attempts.entries()) {
            if (now > attempts.resetTime) {
                this.attempts.delete(key);
                cleaned++;
            }
        }
        
        if (cleaned > 0) {
            logger.debug(`Limpeza de rate limit: ${cleaned} entradas removidas`);
        }
    }

    /**
     * Obtém estatísticas do rate limiter
     * @returns {Object} - Estatísticas
     */
    getStats() {
        const now = Date.now();
        let activeUsers = 0;
        let totalAttempts = 0;
        let limitedUsers = 0;
        
        for (const [key, attempts] of this.attempts.entries()) {
            if (now <= attempts.resetTime) {
                activeUsers++;
                totalAttempts += attempts.count;
                
                // Assumindo limite padrão de 10 para estatísticas
                if (attempts.count >= 10) {
                    limitedUsers++;
                }
            }
        }
        
        return {
            activeUsers,
            totalAttempts,
            limitedUsers,
            totalEntries: this.attempts.size
        };
    }

    /**
     * Formata tempo restante para exibição
     * @param {number} resetTime - Timestamp do reset
     * @returns {string} - Tempo formatado
     */
    static formatTimeRemaining(resetTime) {
        const now = Date.now();
        const remaining = Math.max(0, resetTime - now);
        
        if (remaining === 0) {
            return 'Disponível agora';
        }
        
        const seconds = Math.ceil(remaining / 1000);
        
        if (seconds < 60) {
            return `${seconds} segundo${seconds !== 1 ? 's' : ''}`;
        }
        
        const minutes = Math.ceil(seconds / 60);
        return `${minutes} minuto${minutes !== 1 ? 's' : ''}`;
    }

    /**
     * Cria mensagem de rate limit para o usuário
     * @param {Object} rateLimitInfo - Informações do rate limit
     * @returns {string} - Mensagem formatada
     */
    static createRateLimitMessage(rateLimitInfo) {
        if (!rateLimitInfo || !rateLimitInfo.isLimited) {
            return null;
        }
        
        const timeRemaining = this.formatTimeRemaining(rateLimitInfo.resetTime);
        
        return `⚠️ **Rate Limit Atingido**\n` +
               `Você atingiu o limite de comandos por minuto.\n` +
               `Tente novamente em: **${timeRemaining}**\n` +
               `Comandos restantes: **${rateLimitInfo.remaining}**`;
    }
}

// Instância global do rate limiter
export const rateLimiter = new RateLimiter();