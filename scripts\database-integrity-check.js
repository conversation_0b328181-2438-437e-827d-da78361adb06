import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { logger } from '../src/utils/logger.js';
import { connectDatabase, disconnectDatabase } from '../src/database/connection.js';

// Carrega variáveis de ambiente
dotenv.config();

/**
 * Script de verificação e manutenção da integridade do banco de dados
 * 
 * Este script verifica:
 * 1. Índices órfãos em todas as coleções
 * 2. Campos órfãos nos documentos
 * 3. Referências quebradas entre coleções
 * 4. Consistência dos dados
 * 5. Performance dos índices
 */

async function checkDatabaseIntegrity() {
    try {
        console.log('🔍 Iniciando verificação de integridade do banco de dados...\n');
        
        await connectDatabase();
        const db = mongoose.connection.db;
        
        // Lista todas as coleções
        const collections = await db.listCollections().toArray();
        console.log('📋 Coleções encontradas:');
        collections.forEach((col, i) => {
            console.log(`  ${i + 1}. ${col.name}`);
        });
        console.log('');
        
        const issues = [];
        
        // Verifica cada coleção
        for (const collectionInfo of collections) {
            const collectionName = collectionInfo.name;
            const collection = db.collection(collectionName);
            
            console.log(`🔍 Verificando coleção: ${collectionName}`);
            
            // 1. Verifica índices
            const indexes = await collection.indexes();
            console.log(`  📊 Índices: ${indexes.length}`);
            
            // Verifica se há índices em campos que não existem mais
            const sampleDoc = await collection.findOne();
            if (sampleDoc) {
                const docFields = Object.keys(sampleDoc);
                
                for (const index of indexes) {
                    const indexFields = Object.keys(index.key);
                    
                    for (const field of indexFields) {
                        // Ignora índices especiais do MongoDB
                        if (field === '_id' || field === '$**') continue;
                        
                        // Verifica se o campo existe nos documentos
                        const fieldExists = await collection.countDocuments({ [field]: { $exists: true } });
                        const totalDocs = await collection.countDocuments();
                        
                        if (fieldExists === 0 && totalDocs > 0) {
                            const issue = `Índice órfão encontrado: ${index.name} no campo '${field}' da coleção '${collectionName}'`;
                            issues.push(issue);
                            console.log(`    ⚠️  ${issue}`);
                        }
                    }
                }
            }
            
            // 2. Verifica campos órfãos específicos conhecidos
            if (collectionName === 'products') {
                const orphanFields = ['productId', 'oldId', 'legacyId'];
                
                for (const field of orphanFields) {
                    const count = await collection.countDocuments({ [field]: { $exists: true } });
                    if (count > 0) {
                        const issue = `Campo órfão '${field}' encontrado em ${count} documentos da coleção '${collectionName}'`;
                        issues.push(issue);
                        console.log(`    ⚠️  ${issue}`);
                    }
                }
            }
            
            console.log('');
        }
        
        // 3. Verifica referências quebradas
        console.log('🔗 Verificando referências entre coleções...');
        
        // Verifica referências de produtos para lojas
        const productsCollection = db.collection('products');
        const storesCollection = db.collection('stores');
        
        if (productsCollection && storesCollection) {
            const products = await productsCollection.find({}, { storeId: 1, name: 1 }).toArray();
            let brokenRefs = 0;
            
            for (const product of products) {
                if (product.storeId) {
                    const storeExists = await storesCollection.countDocuments({ _id: product.storeId });
                    if (storeExists === 0) {
                        brokenRefs++;
                        console.log(`    ⚠️  Produto '${product.name}' referencia loja inexistente: ${product.storeId}`);
                    }
                }
            }
            
            if (brokenRefs > 0) {
                issues.push(`${brokenRefs} produtos com referências quebradas para lojas`);
            } else {
                console.log('  ✅ Todas as referências produto->loja estão válidas');
            }
        }
        
        // Verifica referências de itens de estoque para produtos
        const stockItemsCollection = db.collection('stock_items');
        
        if (stockItemsCollection && productsCollection) {
            const stockItems = await stockItemsCollection.find({}, { productId: 1, _id: 1 }).limit(100).toArray();
            let brokenStockRefs = 0;
            
            for (const item of stockItems) {
                if (item.productId) {
                    const productExists = await productsCollection.countDocuments({ _id: item.productId });
                    if (productExists === 0) {
                        brokenStockRefs++;
                    }
                }
            }
            
            if (brokenStockRefs > 0) {
                issues.push(`${brokenStockRefs} itens de estoque com referências quebradas para produtos`);
                console.log(`    ⚠️  ${brokenStockRefs} itens de estoque referenciam produtos inexistentes`);
            } else {
                console.log('  ✅ Referências estoque->produto verificadas (amostra)');
            }
        }
        
        console.log('');
        
        // 4. Relatório final
        console.log('📊 RELATÓRIO DE INTEGRIDADE');
        console.log('=' .repeat(50));
        
        if (issues.length === 0) {
            console.log('✅ Nenhum problema de integridade encontrado!');
            console.log('🎉 Banco de dados está íntegro.');
        } else {
            console.log(`⚠️  ${issues.length} problema(s) encontrado(s):`);
            issues.forEach((issue, i) => {
                console.log(`  ${i + 1}. ${issue}`);
            });
            
            console.log('\n💡 RECOMENDAÇÕES:');
            console.log('  • Execute o script fix-productid-index.js para corrigir índices órfãos');
            console.log('  • Considere executar uma limpeza de dados órfãos');
            console.log('  • Verifique logs de aplicação para identificar origem dos problemas');
        }
        
        console.log('\n✅ Verificação de integridade concluída.');
        
        return issues;
        
    } catch (error) {
        console.error('❌ Erro durante verificação de integridade:', error);
        throw error;
    } finally {
        await disconnectDatabase();
    }
}

// Executa o script se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
    checkDatabaseIntegrity()
        .then((issues) => {
            if (issues.length === 0) {
                console.log('\n🎯 Verificação concluída: banco íntegro!');
                process.exit(0);
            } else {
                console.log(`\n⚠️  Verificação concluída: ${issues.length} problema(s) encontrado(s).`);
                process.exit(1);
            }
        })
        .catch((error) => {
            console.error('\n💥 Erro fatal:', error);
            process.exit(1);
        });
}

export { checkDatabaseIntegrity };
