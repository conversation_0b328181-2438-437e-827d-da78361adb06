import dotenv from 'dotenv';
import mongoose from 'mongoose';

// Carrega variáveis de ambiente
dotenv.config();

async function investigateTesteProduct() {
    try {
        console.log('🔍 Investigando produto "teste" e seus itens de estoque...');
        
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB');
        
        const db = mongoose.connection.db;
        const productsCollection = db.collection('products');
        const stockItemsCollection = db.collection('stock_items');
        const storesCollection = db.collection('stores');
        
        // 1. Busca o produto "teste"
        console.log('\n📦 PRODUTO "TESTE":');
        console.log('=' .repeat(50));
        
        const testeProduct = await productsCollection.findOne({ 
            name: { $regex: /teste/i } 
        });
        
        if (!testeProduct) {
            console.log('❌ Produto "teste" não encontrado');
            return;
        }
        
        console.log('✅ Produto encontrado:');
        console.log(`   ID: ${testeProduct._id}`);
        console.log(`   Nome: ${testeProduct.name}`);
        console.log(`   Descrição: ${testeProduct.description || 'N/A'}`);
        console.log(`   Preço: ${testeProduct.price || 'N/A'}`);
        console.log(`   Status: ${testeProduct.status || 'N/A'}`);
        console.log(`   Categoria: ${testeProduct.category || 'N/A'}`);
        console.log(`   StoreId: ${testeProduct.storeId || 'N/A'}`);
        console.log(`   CreatedBy: ${testeProduct.createdBy || 'N/A'}`);
        console.log(`   Emoji: ${testeProduct.emoji || 'N/A'}`);
        console.log(`   Stock: ${testeProduct.stock || 'N/A'}`);
        console.log(`   CreatedAt: ${testeProduct.createdAt || 'N/A'}`);
        console.log(`   UpdatedAt: ${testeProduct.updatedAt || 'N/A'}`);
        
        // Lista todos os campos do produto
        console.log('\n📋 Todos os campos do produto:');
        Object.keys(testeProduct).forEach(key => {
            console.log(`   ${key}: ${JSON.stringify(testeProduct[key])}`);
        });
        
        // 2. Busca itens de estoque relacionados
        console.log('\n📊 ITENS DE ESTOQUE:');
        console.log('=' .repeat(50));
        
        const stockItems = await stockItemsCollection.find({ 
            productId: testeProduct._id 
        }).toArray();
        
        console.log(`✅ Encontrados ${stockItems.length} itens de estoque:`);
        
        stockItems.forEach((item, index) => {
            console.log(`\n   Item ${index + 1}:`);
            console.log(`     ID: ${item._id}`);
            console.log(`     ProductId: ${item.productId}`);
            console.log(`     Content: ${item.content || 'N/A'}`);
            console.log(`     Status: ${item.status || 'N/A'}`);
            console.log(`     CreatedAt: ${item.createdAt || 'N/A'}`);
            console.log(`     SoldAt: ${item.soldAt || 'N/A'}`);
            console.log(`     SoldTo: ${item.soldTo || 'N/A'}`);
            
            // Lista todos os campos do item
            console.log(`     Todos os campos:`, Object.keys(item));
        });
        
        // 3. Verifica a loja associada
        console.log('\n🏪 LOJA ASSOCIADA:');
        console.log('=' .repeat(50));
        
        if (testeProduct.storeId) {
            const store = await storesCollection.findOne({ _id: testeProduct.storeId });
            if (store) {
                console.log('✅ Loja encontrada:');
                console.log(`   ID: ${store._id}`);
                console.log(`   Nome: ${store.name}`);
                console.log(`   GuildId: ${store.guildId || 'N/A'}`);
                console.log(`   ChannelId: ${store.channelId || 'N/A'}`);
                console.log(`   MessageId: ${store.messageId || 'N/A'}`);
            } else {
                console.log('❌ Loja não encontrada para o storeId:', testeProduct.storeId);
            }
        } else {
            console.log('❌ Produto não tem storeId definido');
        }
        
        // 4. Compara com produtos recém-criados
        console.log('\n🆚 COMPARAÇÃO COM PRODUTOS RECENTES:');
        console.log('=' .repeat(50));
        
        const recentProducts = await productsCollection.find({
            createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Últimas 24h
        }).toArray();
        
        console.log(`✅ Encontrados ${recentProducts.length} produtos recentes:`);
        
        recentProducts.forEach((product, index) => {
            console.log(`\n   Produto ${index + 1}:`);
            console.log(`     ID: ${product._id}`);
            console.log(`     Nome: ${product.name}`);
            console.log(`     Status: ${product.status}`);
            console.log(`     StoreId: ${product.storeId}`);
            console.log(`     Campos: ${Object.keys(product).join(', ')}`);
        });
        
        // 5. Verifica diferenças estruturais
        console.log('\n🔍 ANÁLISE ESTRUTURAL:');
        console.log('=' .repeat(50));
        
        if (recentProducts.length > 0) {
            const recentProduct = recentProducts[0];
            const testeFields = Object.keys(testeProduct);
            const recentFields = Object.keys(recentProduct);
            
            const missingInTeste = recentFields.filter(field => !testeFields.includes(field));
            const extraInTeste = testeFields.filter(field => !recentFields.includes(field));
            
            if (missingInTeste.length > 0) {
                console.log('⚠️  Campos ausentes no produto "teste":');
                missingInTeste.forEach(field => {
                    console.log(`     - ${field}: ${JSON.stringify(recentProduct[field])}`);
                });
            }
            
            if (extraInTeste.length > 0) {
                console.log('ℹ️  Campos extras no produto "teste":');
                extraInTeste.forEach(field => {
                    console.log(`     - ${field}: ${JSON.stringify(testeProduct[field])}`);
                });
            }
            
            if (missingInTeste.length === 0 && extraInTeste.length === 0) {
                console.log('✅ Estrutura de campos idêntica');
            }
        }
        
        await mongoose.disconnect();
        console.log('\n✅ Investigação concluída');
        
    } catch (error) {
        console.error('❌ Erro durante investigação:', error);
        process.exit(1);
    }
}

investigateTesteProduct();
