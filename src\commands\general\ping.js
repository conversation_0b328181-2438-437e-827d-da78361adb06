import { SlashCommandBuilder, EmbedBuilder } from 'discord.js';

export default {
    data: new SlashCommandBuilder()
        .setName('ping')
        .setDescription('Verifica a latência do bot'),
    
    async execute(interaction) {
        // Calcula a latência
        const sent = await interaction.reply({ 
            content: '🏓 Calculando ping...', 
            fetchReply: true 
        });
        
        const roundtripLatency = sent.createdTimestamp - interaction.createdTimestamp;
        const websocketLatency = interaction.client.ws.ping;
        
        // Cria embed com informações de latência
        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('🏓 Pong!')
            .addFields(
                { 
                    name: '📡 Latência da API', 
                    value: `${roundtripLatency}ms`, 
                    inline: true 
                },
                { 
                    name: '💓 Latência do WebSocket', 
                    value: `${websocketLatency}ms`, 
                    inline: true 
                }
            )
            .setTimestamp()
            .setFooter({ 
                text: `Solicitado por ${interaction.user.username}`,
                iconURL: interaction.user.displayAvatarURL()
            });
        
        // Determina a cor baseada na latência
        if (roundtripLatency > 200 || websocketLatency > 200) {
            embed.setColor('#ff0000'); // Vermelho para latência alta
        } else if (roundtripLatency > 100 || websocketLatency > 100) {
            embed.setColor('#ffff00'); // Amarelo para latência média
        }
        
        await interaction.editReply({ 
            content: null, 
            embeds: [embed] 
        });
    }
};
