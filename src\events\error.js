import { Events } from 'discord.js';
import { logger } from '../utils/logger.js';

export default {
    name: Events.Error,
    async execute(error) {
        try {
            await logger.logStructured('ERROR', 'SYSTEM', 'Erro do cliente Discord', {}, {
                error: error.message,
                stack: error.stack,
                name: error.name,
                code: error.code || 'N/A'
            });

        } catch (logError) {
            // Fallback para console se o sistema de log falhar
            console.error('Erro no cliente Discord:', error);
            console.error('Erro ao fazer log do erro:', logError);
        }
    }
};
