import { logger } from '../utils/logger.js';
import { rateLimiter } from '../utils/rateLimiter.js';
import BotConfig from '../models/BotConfig.js';

/**
 * Manipula comandos slash
 * @param {ChatInputCommandInteraction} interaction 
 */
export async function handleSlashCommand(interaction) {
    const { commandName } = interaction;
    const startTime = Date.now();

    try {
        // Log estruturado do início da execução do comando
        await logger.command(`Comando /${commandName} iniciado`, {
            guildId: interaction.guild?.id,
            userId: interaction.user.id,
            command: commandName,
            channel: interaction.channel?.id
        }, {
            guild: interaction.guild?.name || 'DM',
            user: interaction.user.tag
        });

        // Verifica rate limiting se estiver em um servidor
        if (interaction.guild) {
            const config = await BotConfig.findByGuild(interaction.guild.id);

            if (config?.rateLimiting?.isEnabled !== false) {
                const rateLimitConfig = {
                    windowMs: config?.rateLimiting?.windowMs || 60000,
                    maxRequests: config?.rateLimiting?.maxRequests || 10
                };

                const rateLimitResult = await rateLimiter.checkRateLimit(
                    interaction.user.id,
                    interaction.guild.id,
                    rateLimitConfig
                );

                if (rateLimitResult.isLimited) {
                    const warningMessage = rateLimiter.formatWarningMessage(rateLimitResult);

                    await interaction.reply({
                        content: warningMessage,
                        ephemeral: true
                    });

                    // Log do rate limit usando sistema estruturado
                    await logger.security(`Rate limit atingido para comando /${commandName}`, {
                        guildId: interaction.guild.id,
                        userId: interaction.user.id,
                        command: commandName
                    }, {
                        remainingTime: rateLimitResult.remainingTime,
                        requestCount: rateLimitResult.requestCount,
                        maxRequests: rateLimitConfig.maxRequests
                    });

                    return;
                }
            }
        }

        // Busca o comando na coleção de comandos do cliente
        const command = interaction.client.commands.get(commandName);

        if (!command) {
            await logger.logStructured('WARN', 'COMMAND', `Comando não encontrado: ${commandName}`, {
                guildId: interaction.guild?.id,
                userId: interaction.user.id,
                command: commandName
            });

            await interaction.reply({
                content: '❌ Comando não encontrado.',
                ephemeral: true
            });
            return;
        }

        // Executa o comando
        await command.execute(interaction);

        const executionTime = Date.now() - startTime;

        // Log de sucesso com tempo de execução
        await logger.command(`Comando /${commandName} executado com sucesso`, {
            guildId: interaction.guild?.id,
            userId: interaction.user.id,
            command: commandName
        }, {
            executionTime: `${executionTime}ms`
        });

        // Log de performance se demorou muito
        if (executionTime > 5000) {
            await logger.performance(`Comando lento detectado: /${commandName}`, {
                guildId: interaction.guild?.id,
                command: commandName
            }, {
                executionTime,
                threshold: 5000
            });
        }

    } catch (error) {
        const executionTime = Date.now() - startTime;

        // Log estruturado do erro
        await logger.logStructured('ERROR', 'COMMAND', `Erro ao executar comando /${commandName}`, {
            guildId: interaction.guild?.id,
            userId: interaction.user.id,
            command: commandName
        }, {
            error: error.message,
            stack: error.stack,
            executionTime: `${executionTime}ms`
        });

        const errorMessage = {
            content: '❌ Ocorreu um erro ao executar este comando.',
            ephemeral: true
        };

        // Verifica se a interação já foi respondida
        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        } catch (replyError) {
            await logger.logStructured('ERROR', 'COMMAND', 'Erro ao responder interação após falha', {
                guildId: interaction.guild?.id,
                userId: interaction.user.id,
                command: commandName
            }, {
                originalError: error.message,
                replyError: replyError.message
            });
        }
    }
}
