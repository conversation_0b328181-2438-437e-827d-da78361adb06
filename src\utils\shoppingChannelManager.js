import { ChannelType, PermissionFlagsBits, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';
import { logger } from './logger.js';
import ShoppingCart from '../models/ShoppingCart.js';
import Store from '../models/Store.js';
import { COLORS, EMOJIS, BOT_CONFIG } from '../config/constants.js';

/**
 * Gerenciador de canais de compras privados
 */
export class ShoppingChannelManager {
    /**
     * Cria um canal privado de compras para o usuário
     * @param {Guild} guild - Guild do Discord
     * @param {User} user - Usuário que iniciou a compra
     * @param {Object} store - <PERSON><PERSON> da loja
     * @param {Object} initialProduct - Produto inicial (opcional)
     * @returns {Promise<Object>} Resultado da criação do canal
     */
    static async createShoppingChannel(guild, user, store, initialProduct = null) {
        try {
            // Validações iniciais
            if (!guild) {
                throw new Error('Guild é obrigatório');
            }
            if (!user) {
                throw new Error('User é obrigatório');
            }
            if (!store) {
                throw new Error('Store é obrigatório');
            }
            if (!store.name) {
                throw new Error('Store deve ter um nome');
            }
            if (!store._id) {
                throw new Error('Store deve ter um _id');
            }

            // Verifica permissões do bot
            const botMember = guild.members.me;
            if (!botMember) {
                throw new Error('Bot não encontrado no servidor');
            }

            const requiredPermissions = [
                PermissionFlagsBits.ManageChannels,
                PermissionFlagsBits.ViewChannel,
                PermissionFlagsBits.SendMessages,
                PermissionFlagsBits.EmbedLinks,
                PermissionFlagsBits.ManageRoles
            ];

            const missingPermissions = requiredPermissions.filter(perm =>
                !botMember.permissions.has(perm)
            );

            if (missingPermissions.length > 0) {
                const permissionNames = missingPermissions.map(perm => {
                    switch(perm) {
                        case PermissionFlagsBits.ManageChannels: return 'Gerenciar Canais';
                        case PermissionFlagsBits.ViewChannel: return 'Ver Canais';
                        case PermissionFlagsBits.SendMessages: return 'Enviar Mensagens';
                        case PermissionFlagsBits.EmbedLinks: return 'Incorporar Links';
                        case PermissionFlagsBits.ManageRoles: return 'Gerenciar Cargos';
                        default: return perm.toString();
                    }
                });
                throw new Error(`Bot não tem as permissões necessárias: ${permissionNames.join(', ')}`);
            }

            // Verifica se o usuário já tem um carrinho ativo
            const existingCart = await ShoppingCart.findActiveByUser(user.id, guild.id);
            if (existingCart) {
                const existingChannel = guild.channels.cache.get(existingCart.channelId);
                if (existingChannel) {
                    return {
                        success: true,
                        channel: existingChannel,
                        cart: existingCart,
                        isExisting: true
                    };
                } else {
                    // Canal não existe mais, marca carrinho como abandonado
                    await existingCart.markAsAbandoned();
                }
            }

            // Busca ou cria categoria para canais de compras
            const categoryName = '🛒 Compras Privadas';
            let category = guild.channels.cache.find(c =>
                c.type === ChannelType.GuildCategory && c.name === categoryName
            );

            logger.info(`Buscando categoria: ${categoryName}, encontrada: ${!!category}`);

            if (!category) {
                logger.info('Criando nova categoria de compras...');
                try {
                    category = await guild.channels.create({
                        name: categoryName,
                        type: ChannelType.GuildCategory,
                        permissionOverwrites: [
                            {
                                id: guild.roles.everyone.id,
                                deny: [PermissionFlagsBits.ViewChannel]
                            },
                            {
                                id: guild.members.me.id,
                                allow: [
                                    PermissionFlagsBits.ViewChannel,
                                    PermissionFlagsBits.SendMessages,
                                    PermissionFlagsBits.EmbedLinks,
                                    PermissionFlagsBits.ManageMessages,
                                    PermissionFlagsBits.AddReactions
                                ]
                            }
                        ]
                    });

                    logger.info(`Categoria criada com sucesso: ${category.name} (${category.id})`);
                } catch (categoryError) {
                    logger.error('Erro ao criar categoria:', {
                        message: categoryError.message,
                        code: categoryError.code,
                        stack: categoryError.stack
                    });
                    throw categoryError;
                }
            }

            // Cria nome único para o canal
            const timestamp = Date.now().toString(36).slice(-6);
            let channelName = `carrinho-${user.username}-${timestamp}`.toLowerCase()
                .replace(/[^a-z0-9\-]/g, '');

            // Garante que o nome não está vazio e tem pelo menos 2 caracteres
            if (channelName.length < 2) {
                channelName = `carrinho-user-${timestamp}`;
            }

            // Limita o tamanho do nome (Discord permite até 100 caracteres)
            if (channelName.length > 90) {
                channelName = channelName.substring(0, 90);
            }

            logger.info(`Criando canal: ${channelName} para usuário ${user.tag}`);

            // Cria o canal privado
            let channel;
            try {
                channel = await guild.channels.create({
                    name: channelName,
                    type: ChannelType.GuildText,
                    parent: category.id,
                    topic: `Carrinho de compras de ${user.tag} - Loja: ${store.name}`,
                    permissionOverwrites: [
                        {
                            id: guild.roles.everyone.id,
                            deny: [PermissionFlagsBits.ViewChannel]
                        },
                        {
                            id: user.id,
                            allow: [
                                PermissionFlagsBits.ViewChannel,
                                PermissionFlagsBits.SendMessages,
                                PermissionFlagsBits.ReadMessageHistory,
                                PermissionFlagsBits.AddReactions
                            ]
                        },
                        {
                            id: guild.members.me.id,
                            allow: [
                                PermissionFlagsBits.ViewChannel,
                                PermissionFlagsBits.SendMessages,
                                PermissionFlagsBits.EmbedLinks,
                                PermissionFlagsBits.ManageMessages,
                                PermissionFlagsBits.AddReactions
                            ]
                        }
                    ]
                });

                logger.info(`Canal criado com sucesso: ${channel.name} (${channel.id})`);
            } catch (channelError) {
                logger.error('Erro ao criar canal:', {
                    message: channelError.message,
                    code: channelError.code,
                    stack: channelError.stack,
                    channelName,
                    categoryId: category.id
                });
                throw channelError;
            }

            // Adiciona permissões para administradores da loja
            const adminRoles = guild.roles.cache.filter(role => 
                role.permissions.has(PermissionFlagsBits.Administrator) && 
                role.id !== guild.roles.everyone.id
            );

            for (const role of adminRoles.values()) {
                await channel.permissionOverwrites.create(role.id, {
                    ViewChannel: true,
                    SendMessages: true,
                    ReadMessageHistory: true
                });
            }

            // Cria o carrinho de compras no banco de dados
            logger.info('Criando carrinho no banco de dados...');
            const expirationTime = new Date(Date.now() + (2 * 60 * 60 * 1000)); // 2 horas

            let cart;
            try {
                cart = new ShoppingCart({
                    userId: user.id,
                    userTag: user.tag,
                    channelId: channel.id,
                    guildId: guild.id,
                    storeId: store._id,
                    storeName: store.name,
                    expiresAt: expirationTime
                });

                // Adiciona produto inicial se fornecido
                if (initialProduct) {
                    logger.info(`Adicionando produto inicial: ${initialProduct.name}`);
                    await cart.addItem(initialProduct, 1);
                }

                await cart.save();
                logger.info(`Carrinho salvo com sucesso: ${cart._id}`);
            } catch (cartError) {
                logger.error('Erro ao criar/salvar carrinho:', {
                    message: cartError.message,
                    code: cartError.code,
                    stack: cartError.stack,
                    userId: user.id,
                    channelId: channel.id,
                    storeId: store._id
                });

                // Se falhou ao criar carrinho, remove o canal criado
                try {
                    await channel.delete('Erro ao criar carrinho');
                } catch (deleteError) {
                    logger.error('Erro ao deletar canal após falha do carrinho:', deleteError);
                }

                throw cartError;
            }

            // Envia mensagem de boas-vindas
            try {
                await this.sendWelcomeMessage(channel, user, store, cart);
                logger.info('Mensagem de boas-vindas enviada com sucesso');
            } catch (welcomeError) {
                logger.error('Erro ao enviar mensagem de boas-vindas:', {
                    message: welcomeError.message,
                    code: welcomeError.code,
                    channelId: channel.id
                });
                // Não falha a criação do canal por causa da mensagem
            }

            logger.info(`Canal de compras criado com sucesso: ${channel.name} para ${user.tag}`);

            return {
                success: true,
                channel,
                cart,
                isExisting: false
            };

        } catch (error) {
            // Log detalhado do erro
            logger.error('Erro ao criar canal de compras:', {
                message: error.message,
                stack: error.stack,
                name: error.name,
                code: error.code,
                userId: user?.id,
                guildId: guild?.id,
                storeName: store?.name,
                storeId: store?._id
            });

            return {
                success: false,
                error: error.message || 'Erro desconhecido ao criar canal'
            };
        }
    }

    /**
     * Envia mensagem de boas-vindas no canal de compras
     * @param {TextChannel} channel - Canal de compras
     * @param {User} user - Usuário
     * @param {Object} store - Dados da loja
     * @param {ShoppingCart} cart - Carrinho de compras
     */
    static async sendWelcomeMessage(channel, user, store, cart) {
        const embed = new EmbedBuilder()
            .setTitle(`🛒 Bem-vindo ao seu Carrinho de Compras!`)
            .setDescription(
                `Olá ${user}, este é seu canal privado de compras da **${store.name}**!\n\n` +
                `🔒 **Este canal é privado** - apenas você e os administradores podem vê-lo.\n` +
                `⏰ **Sessão expira em:** <t:${Math.floor(cart.expiresAt.getTime() / 1000)}:R>\n\n` +
                `**Como usar:**\n` +
                `• Use os botões abaixo para gerenciar seu carrinho\n` +
                `• Adicione mais produtos navegando pela loja\n` +
                `• Finalize sua compra quando estiver pronto\n\n` +
                `💡 **Dica:** Sua sessão será automaticamente limpa após 2 horas de inatividade.`
            )
            .setColor(COLORS.SUCCESS)
            .setThumbnail(store.banner || null)
            .setFooter({
                text: `Loja: ${store.name} | Carrinho ID: ${cart._id.toString().slice(-8)}`,
                iconURL: user.displayAvatarURL()
            })
            .setTimestamp();

        // Apenas botão de cancelar compra no embed de boas-vindas
        const actionRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('cart_cancel_purchase')
                    .setLabel('Cancelar Compra')
                    .setEmoji('❌')
                    .setStyle(ButtonStyle.Danger)
            );

        await channel.send({
            content: `${user}`,
            embeds: [embed],
            components: [actionRow]
        });

        // Cria o embed fixo do carrinho logo após a mensagem de boas-vindas
        const { fixedCartEmbedHandler } = await import('../handlers/fixedCartEmbedHandler.js');
        await fixedCartEmbedHandler.createOrUpdateCartEmbed(channel, cart);
    }

    /**
     * Envia resumo do carrinho
     * @param {TextChannel} channel - Canal de compras
     * @param {ShoppingCart} cart - Carrinho de compras
     */
    static async sendCartSummary(channel, cart) {
        if (cart.items.length === 0) {
            const embed = new EmbedBuilder()
                .setTitle('🛒 Seu Carrinho')
                .setDescription('Seu carrinho está vazio. Use o botão "Adicionar Produtos" para começar!')
                .setColor(COLORS.WARNING);

            await channel.send({ embeds: [embed] });
            return;
        }

        const embed = new EmbedBuilder()
            .setTitle(`🛒 Seu Carrinho (${cart.itemCount} ${cart.itemCount === 1 ? 'item' : 'itens'})`)
            .setColor(COLORS.PRIMARY)
            .setFooter({
                text: `Total: ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${cart.subtotal.toFixed(2)}`
            });

        // Adiciona cada item como field
        cart.items.forEach((item, index) => {
            const emoji = item.productEmoji || '📦';
            embed.addFields({
                name: `${emoji} ${item.productName}`,
                value: `**Quantidade:** ${item.quantity}\n**Preço unitário:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${item.unitPrice.toFixed(2)}\n**Total:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${item.totalPrice.toFixed(2)}`,
                inline: true
            });
        });

        await channel.send({ embeds: [embed] });
    }

    /**
     * Remove canal de compras e limpa dados
     * @param {TextChannel} channel - Canal a ser removido
     * @param {string} reason - Motivo da remoção
     */
    static async cleanupShoppingChannel(channel, reason = 'Sessão finalizada') {
        try {
            // Busca o carrinho associado
            const cart = await ShoppingCart.findByChannel(channel.id);
            
            if (cart && cart.status === 'active') {
                await cart.markAsAbandoned();
            }

            // Remove o canal
            await channel.delete(reason);

            await logger.system(`Canal de compras removido: ${channel.name} - ${reason}`);

            return { success: true };

        } catch (error) {
            await logger.error('Erro ao limpar canal de compras:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Limpa canais de compras expirados
     * @param {Guild} guild - Guild do Discord
     */
    static async cleanupExpiredChannels(guild) {
        try {
            const expiredCarts = await ShoppingCart.findExpiredCarts();
            let cleanedCount = 0;

            for (const cart of expiredCarts) {
                if (cart.guildId === guild.id) {
                    const channel = guild.channels.cache.get(cart.channelId);
                    if (channel) {
                        await this.cleanupShoppingChannel(channel, 'Sessão expirada');
                        cleanedCount++;
                    } else {
                        // Canal já foi removido, marca carrinho como abandonado
                        await cart.markAsAbandoned();
                    }
                }
            }

            if (cleanedCount > 0) {
                await logger.system(`${cleanedCount} canais de compras expirados foram limpos`);
            }

            return cleanedCount;

        } catch (error) {
            await logger.error('Erro ao limpar canais expirados:', error);
            return 0;
        }
    }
}

export default ShoppingChannelManager;
