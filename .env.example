# Discord Bot Configuration
DISCORD_TOKEN=seu_token_do_bot_aqui
CLIENT_ID=id_da_aplicacao_discord
GUILD_ID=id_do_servidor_para_testes

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/discord-store-bot
# Para MongoDB Atlas: mongodb+srv://username:<EMAIL>/discord-store-bot

# Bot Configuration
BOT_PREFIX=!
BOT_OWNER_ID=seu_discord_id_aqui
NODE_ENV=development

# Logging
LOG_LEVEL=info
ADMIN_LOG_CHANNEL_ID=
PUBLIC_LOG_CHANNEL_ID=

# MercadoPago Configuration
MERCADOPAGO_ACCESS_TOKEN=
MERCADOPAGO_PUBLIC_KEY=
MERCADOPAGO_WEBHOOK_SECRET=

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=10
