# 🏪 Sistema de Criação de Lojas

Este documento explica como usar o sistema de criação de lojas implementado no bot Discord.

## 📋 Funcionalidades

### Comando `/criar-loja`
- **Permissão**: Apenas o dono do bot pode executar
- **Função**: Abre um modal para criação de uma nova loja
- **Resultado**: Cria um canal privado com embed de produtos

## 🔧 Configuração Inicial

### 1. Configurar o Dono do Bot
No arquivo `.env`, adicione seu Discord ID:
```env
BOT_OWNER_ID=SEU_DISCORD_ID_AQUI
```

**Como descobrir seu Discord ID:**
1. Ative o Modo Desenvolvedor no Discord (Configurações > Avançado > Modo Desenvolvedor)
2. Clique com botão direito no seu nome/avatar
3. Selecione "Copiar ID"

### 2. Permissões Necessárias do Bot
O bot precisa das seguintes permissões no servidor:
- `Gerenciar Canais` (ManageChannels)
- `Gerenciar Cargos` (ManageRoles) 
- `Enviar Mensagens` (SendMessages)
- `Incorporar Links` (EmbedLinks)

## 🚀 Como Usar

### 1. Executar o Comando
```
/criar-loja
```

### 2. Preencher o Modal
O modal contém os seguintes campos obrigatórios:

#### **Banner da Loja**
- URL da imagem que será exibida no embed
- Formatos aceitos: JPG, PNG, GIF, WEBP
- Exemplo: `https://exemplo.com/banner.png`

#### **Nome da Loja**
- Nome que aparecerá no título do embed
- Mínimo: 3 caracteres
- Máximo: 100 caracteres
- Exemplo: `Loja de Eletrônicos`

#### **Cor da Loja**
- Cor do embed da loja
- Formatos aceitos:
  - Código hexadecimal: `#FF0000`
  - Nome da cor: `red`, `blue`, `green`, etc.
- Exemplo: `#0099ff` ou `blue`

#### **Descrição da Loja**
- Descrição detalhada da loja
- Mínimo: 10 caracteres
- Máximo: 1000 caracteres
- Exemplo: `Loja especializada em produtos eletrônicos com os melhores preços!`

### 3. Resultado da Criação

Após o preenchimento correto, o bot irá:

1. **Criar um canal privado** com o nome da loja
2. **Configurar permissões**:
   - @everyone: NÃO pode ver o canal
   - Administradores: Podem ver e interagir
   - Bot: Permissões completas
3. **Enviar embed** com informações da loja
4. **Adicionar dropdown** para seleção de produtos

## 📦 Dropdown de Produtos

O dropdown no canal da loja funciona da seguinte forma:

### Estados do Dropdown:
- **Produtos disponíveis**: Lista todos os produtos ativos com estoque
- **Nenhum produto**: Mostra "Nenhum produto disponível" se não houver produtos
- **Opção cancelar**: Sempre presente para fechar o menu

### Seleção de Produto:
Ao selecionar um produto, o usuário recebe um embed com:
- Nome e descrição do produto
- Preço formatado
- Estoque disponível
- Categoria
- Tipo (Digital/Físico)
- Imagem (se disponível)
- Estatísticas (ID, vendas)

## 🗄️ Estrutura do Banco de Dados

### Modelo Store
```javascript
{
  name: String,           // Nome da loja
  description: String,    // Descrição da loja
  banner: String,         // URL do banner
  color: String,          // Cor em hex
  guildId: String,        // ID do servidor
  channelId: String,      // ID do canal criado
  messageId: String,      // ID da mensagem do embed
  isActive: Boolean,      // Status da loja
  allowedRoles: [String], // Cargos permitidos
  createdBy: String,      // ID do criador
  timestamps: true        // Data de criação/modificação
}
```

## ⚠️ Validações e Erros

### Validações Implementadas:
- **URL do Banner**: Deve ser uma URL válida de imagem
- **Cor**: Deve ser hex válido ou nome de cor reconhecido
- **Nome único**: Não pode haver duas lojas com o mesmo nome no servidor
- **Permissões**: Verifica se o bot tem permissões necessárias

### Mensagens de Erro Comuns:
- `❌ Apenas o dono do bot pode executar este comando.`
- `❌ URL do banner inválida. Use uma URL válida de imagem.`
- `❌ Cor inválida. Use formato hex (#FFFFFF) ou nome de cor.`
- `❌ Já existe uma loja com este nome no servidor`
- `❌ O bot precisa das seguintes permissões: ...`

## 🔍 Logs e Debug

O sistema registra logs detalhados:
- Execução de comandos
- Criação de lojas
- Seleção de produtos
- Erros e exceções

Para debug, verifique os logs do console do bot.

## 📝 Próximos Passos

Este sistema pode ser expandido com:
- Comando para editar lojas existentes
- Sistema de carrinho de compras
- Integração com pagamentos
- Gestão de estoque automática
- Relatórios de vendas
- Sistema de cupons/descontos

## 🆘 Suporte

Em caso de problemas:
1. Verifique se o `BOT_OWNER_ID` está configurado corretamente
2. Confirme se o bot tem as permissões necessárias
3. Verifique os logs do console para erros específicos
4. Teste com URLs de imagem válidas e acessíveis
