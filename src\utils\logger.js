/**
 * Sistema de logging estruturado e eficiente
 * Integra logs de console com logs para canais Discord
 */

const LOG_LEVELS = {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3
};

const COLORS = {
    ERROR: '\x1b[31m', // Vermelho
    WARN: '\x1b[33m',  // Amarelo
    INFO: '\x1b[36m',  // Ciano
    DEBUG: '\x1b[35m', // Magenta
    RESET: '\x1b[0m'   // Reset
};

// Tipos de log para categorização
export const LOG_TYPES = {
    SYSTEM: 'SYSTEM',           // Logs do sistema (inicialização, conexões)
    COMMAND: 'COMMAND',         // Execução de comandos
    EVENT: 'EVENT',             // Eventos do Discord
    DATABASE: 'DATABASE',       // Operações de banco de dados
    API: 'API',                 // Integrações com APIs externas
    SECURITY: 'SECURITY',       // Logs de segurança e moderação
    ERROR: 'ERROR',             // Erros e exceções
    USER_ACTION: 'USER_ACTION', // Ações de usuários
    PERFORMANCE: 'PERFORMANCE'  // Logs de performance
};

class Logger {
    constructor() {
        this.level = this.getLogLevel();
        this.discordLogger = null; // Será definido após importação para evitar dependência circular
    }

    getLogLevel() {
        const envLevel = (process.env.LOG_LEVEL && process.env.LOG_LEVEL.toUpperCase()) || 'INFO';
        return LOG_LEVELS[envLevel] !== undefined ? LOG_LEVELS[envLevel] : LOG_LEVELS.INFO;
    }

    setDiscordLogger(discordLogger) {
        this.discordLogger = discordLogger;
    }

    formatMessage(level, message, ...args) {
        const timestamp = new Date().toISOString();
        const color = COLORS[level];
        const reset = COLORS.RESET;

        const formattedArgs = args.length > 0 ? ' ' + args.map(arg =>
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ') : '';

        return `${color}[${timestamp}] [${level}]${reset} ${message}${formattedArgs}`;
    }

    log(level, message, ...args) {
        if (LOG_LEVELS[level] <= this.level) {
            console.log(this.formatMessage(level, message, ...args));
        }
    }

    /**
     * Log estruturado com contexto adicional
     * @param {string} level - Nível do log (ERROR, WARN, INFO, DEBUG)
     * @param {string} type - Tipo do log (SYSTEM, COMMAND, etc.)
     * @param {string} message - Mensagem principal
     * @param {Object} context - Contexto adicional (usuário, guild, comando, etc.)
     * @param {Object} data - Dados adicionais
     */
    async logStructured(level, type, message, context = {}, data = {}) {
        // Log no console
        const contextStr = Object.keys(context).length > 0 ?
            ` [${Object.entries(context).map(([k, v]) => `${k}:${v}`).join(', ')}]` : '';

        this.log(level, `[${type}]${contextStr} ${message}`, data);

        // Log para Discord se configurado e for um nível importante
        if (this.discordLogger && (level === 'ERROR' || level === 'WARN' || type === 'SECURITY')) {
            try {
                await this.discordLogger.logToDiscord(level, type, message, context, data);
            } catch (error) {
                // Evita loop infinito de logs
                console.error('Erro ao enviar log para Discord:', error);
            }
        }
    }

    // Métodos de conveniência para logs estruturados
    async system(message, context = {}, data = {}) {
        await this.logStructured('INFO', LOG_TYPES.SYSTEM, message, context, data);
    }

    async command(message, context = {}, data = {}) {
        await this.logStructured('INFO', LOG_TYPES.COMMAND, message, context, data);
    }

    async event(message, context = {}, data = {}) {
        await this.logStructured('INFO', LOG_TYPES.EVENT, message, context, data);
    }

    async database(message, context = {}, data = {}) {
        await this.logStructured('INFO', LOG_TYPES.DATABASE, message, context, data);
    }

    async api(message, context = {}, data = {}) {
        await this.logStructured('INFO', LOG_TYPES.API, message, context, data);
    }

    async security(message, context = {}, data = {}) {
        await this.logStructured('WARN', LOG_TYPES.SECURITY, message, context, data);
    }

    async userAction(message, context = {}, data = {}) {
        await this.logStructured('INFO', LOG_TYPES.USER_ACTION, message, context, data);
    }

    async performance(message, context = {}, data = {}) {
        await this.logStructured('DEBUG', LOG_TYPES.PERFORMANCE, message, context, data);
    }

    // Métodos tradicionais mantidos para compatibilidade
    error(message, ...args) {
        this.log('ERROR', message, ...args);
    }

    warn(message, ...args) {
        this.log('WARN', message, ...args);
    }

    info(message, ...args) {
        this.log('INFO', message, ...args);
    }

    debug(message, ...args) {
        this.log('DEBUG', message, ...args);
    }
}

export const logger = new Logger();
