# 🤖 Discord Store Bot

Bot Discord modular para gerenciamento de loja com integração MongoDB, desenvolvido com Discord.js v14.

## ✨ Características

- **Arquitetura Modular**: Separação clara de responsabilidades com carregamento automático
- **Discord.js v14**: Utiliza a versão mais recente com comandos slash
- **MongoDB Integration**: Integração completa com MongoDB usando Mongoose
- **Sistema de Logs**: Sistema de logging colorido e configurável
- **Tratamento de Erros**: Tratamento robusto de erros em toda a aplicação
- **Estrutura Extensível**: Fácil adição de novos comandos e funcionalidades

## 📁 Estrutura do Projeto

```
discord-store-bot/
├── src/
│   ├── commands/           # Comandos organizados por categoria
│   │   ├── general/        # Comandos gerais (ping, info)
│   │   └── store/          # Comandos da loja (balance, products)
│   ├── events/             # Eventos do Discord (ready, interactionCreate)
│   ├── handlers/           # Manipuladores de interações
│   ├── models/             # Modelos MongoDB (User, Product, Order)
│   ├── database/           # Configuração do banco de dados
│   └── utils/              # Utilitários (logger, loaders)
├── scripts/                # Scripts auxiliares
│   └── deploy-commands.js  # Deploy de comandos slash
├── index.js               # Arquivo principal
├── package.json           # Dependências e scripts
├── .env.example          # Exemplo de variáveis de ambiente
└── README.md             # Este arquivo
```

## 🚀 Instalação

### Pré-requisitos

- Node.js 16.11.0 ou superior
- MongoDB (local ou MongoDB Atlas)
- Bot Discord criado no Discord Developer Portal

### 1. Clone e instale dependências

```bash
git clone <seu-repositorio>
cd discord-store-bot
npm install
```

### 2. Configure as variáveis de ambiente

Copie o arquivo `.env.example` para `.env` e configure:

```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas configurações:

```env
# Discord Bot Configuration
DISCORD_TOKEN=seu_token_do_bot_aqui
CLIENT_ID=id_da_aplicacao_discord
GUILD_ID=id_do_servidor_para_testes

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/discord-store-bot

# Bot Configuration
NODE_ENV=development
LOG_LEVEL=info
```

### 3. Configure o MongoDB

**Opção 1: MongoDB Local**
```bash
# Instale o MongoDB localmente
# A URI padrão é: mongodb://localhost:27017/discord-store-bot
```

**Opção 2: MongoDB Atlas**
```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/discord-store-bot
```

### 4. Registre os comandos slash

```bash
npm run deploy-commands
```

### 5. Inicie o bot

```bash
# Desenvolvimento (com nodemon)
npm run dev

# Produção
npm start
```

## 🔧 Configuração do Bot Discord

1. Acesse o [Discord Developer Portal](https://discord.com/developers/applications)
2. Crie uma nova aplicação
3. Vá para a seção "Bot" e crie um bot
4. Copie o token e adicione ao `.env`
5. Na seção "General Information", copie o Application ID para `CLIENT_ID`
6. Convide o bot para seu servidor com as permissões necessárias

### Permissões Necessárias

- `Send Messages`
- `Use Slash Commands`
- `Embed Links`
- `Read Message History`
- `Add Reactions`

## 📋 Comandos Disponíveis

### Comandos Gerais
- `/ping` - Verifica a latência do bot
- `/info` - Mostra informações sobre o bot

### Comandos da Loja
- `/balance [usuario]` - Verifica saldo (próprio ou de outro usuário para admins)
- `/products [categoria] [pagina]` - Lista produtos disponíveis

## 🗄️ Modelos de Dados

### User (Usuário)
- Informações do Discord (ID, username)
- Saldo e histórico financeiro
- Configurações e preferências
- Endereço para entregas

### Product (Produto)
- Informações básicas (nome, descrição, preço)
- Estoque e categoria
- Imagens e metadados
- Estatísticas de vendas

### Order (Pedido)
- Itens do pedido e valores
- Status e informações de pagamento
- Endereço de entrega
- Histórico de alterações

## 🔨 Desenvolvimento

### Adicionando Novos Comandos

1. Crie um arquivo na pasta apropriada em `src/commands/`
2. Use a estrutura padrão:

```javascript
import { SlashCommandBuilder } from 'discord.js';

export default {
    data: new SlashCommandBuilder()
        .setName('meucomando')
        .setDescription('Descrição do comando'),
    
    async execute(interaction) {
        await interaction.reply('Olá mundo!');
    }
};
```

3. Execute `npm run deploy-commands` para registrar

### Adicionando Novos Eventos

1. Crie um arquivo em `src/events/`
2. Use a estrutura padrão:

```javascript
import { Events } from 'discord.js';

export default {
    name: Events.EventName,
    once: false, // true para eventos que executam apenas uma vez
    async execute(...args) {
        // Lógica do evento
    }
};
```

### Sistema de Logs

Configure o nível de log através da variável `LOG_LEVEL`:
- `ERROR`: Apenas erros
- `WARN`: Avisos e erros
- `INFO`: Informações, avisos e erros (padrão)
- `DEBUG`: Todos os logs incluindo debug

## 🚀 Deploy em Produção

1. Configure `NODE_ENV=production` no `.env`
2. Use um gerenciador de processos como PM2:

```bash
npm install -g pm2
pm2 start index.js --name "discord-store-bot"
```

3. Configure um proxy reverso (nginx) se necessário
4. Configure backups automáticos do MongoDB

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🆘 Suporte

Se você encontrar problemas ou tiver dúvidas:

1. Verifique os logs do bot
2. Confirme se todas as variáveis de ambiente estão configuradas
3. Verifique se o MongoDB está acessível
4. Confirme se o bot tem as permissões necessárias no Discord

## 🔄 Próximos Passos

Esta é uma base sólida para expansão. Considere implementar:

- Sistema de pagamentos (PIX, cartão)
- Carrinho de compras
- Sistema de cupons e promoções
- Painel administrativo web
- Notificações automáticas
- Sistema de avaliações
- Integração com APIs de entrega
