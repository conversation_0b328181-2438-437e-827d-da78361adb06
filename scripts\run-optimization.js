import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';
import { spawn } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Master Database Optimization Runner
 * 
 * This script orchestrates the complete database optimization process:
 * 1. Pre-optimization validation
 * 2. Backup creation
 * 3. Database optimization
 * 4. Performance monitoring setup
 * 5. Post-optimization validation
 */

class OptimizationRunner {
    constructor() {
        this.dryRun = process.argv.includes('--dry-run');
        this.skipBackup = process.argv.includes('--skip-backup');
        this.verbose = process.argv.includes('--verbose');
        this.force = process.argv.includes('--force');
        
        this.results = {
            startTime: Date.now(),
            steps: [],
            success: false,
            backupPath: null,
            reportPath: null
        };
    }

    log(message, level = 'INFO') {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] [${level}] ${message}`);
    }

    async runCommand(command, args = [], options = {}) {
        return new Promise((resolve, reject) => {
            const child = spawn(command, args, {
                stdio: 'pipe',
                shell: true,
                ...options
            });

            let stdout = '';
            let stderr = '';

            child.stdout?.on('data', (data) => {
                stdout += data.toString();
                if (this.verbose) {
                    process.stdout.write(data);
                }
            });

            child.stderr?.on('data', (data) => {
                stderr += data.toString();
                if (this.verbose) {
                    process.stderr.write(data);
                }
            });

            child.on('close', (code) => {
                if (code === 0) {
                    resolve({ stdout, stderr, code });
                } else {
                    reject(new Error(`Command failed with code ${code}: ${stderr}`));
                }
            });

            child.on('error', reject);
        });
    }

    async checkPrerequisites() {
        this.log('=== CHECKING PREREQUISITES ===');
        const step = { name: 'prerequisites', startTime: Date.now(), success: false };

        try {
            // Check if MongoDB is accessible
            await this.runCommand('node', ['-e', `
                import mongoose from 'mongoose';
                import dotenv from 'dotenv';
                dotenv.config();
                try {
                    await mongoose.connect(process.env.MONGODB_URI);
                    console.log('MongoDB connection: OK');
                    await mongoose.disconnect();
                    process.exit(0);
                } catch (error) {
                    console.error('MongoDB connection failed:', error.message);
                    process.exit(1);
                }
            `]);

            // Check if node-cache is installed
            try {
                await this.runCommand('node', ['-e', 'import NodeCache from "node-cache"; console.log("node-cache: OK");']);
            } catch (error) {
                this.log('Installing node-cache dependency...', 'WARN');
                await this.runCommand('npm', ['install', 'node-cache']);
            }

            // Check disk space (at least 1GB free)
            const stats = await fs.statSync('.');
            // Note: This is a simplified check, in production you'd want to check actual disk space

            step.success = true;
            this.log('✅ Prerequisites check passed');

        } catch (error) {
            step.error = error.message;
            this.log(`❌ Prerequisites check failed: ${error.message}`, 'ERROR');
            throw error;
        } finally {
            step.duration = Date.now() - step.startTime;
            this.results.steps.push(step);
        }
    }

    async createBackup() {
        if (this.skipBackup) {
            this.log('Skipping backup creation');
            return null;
        }

        this.log('=== CREATING BACKUP ===');
        const step = { name: 'backup', startTime: Date.now(), success: false };

        try {
            const args = ['scripts/database-migration.js'];
            if (this.verbose) args.push('--verbose');
            args.push('--skip-backup'); // We'll handle backup separately

            // Run migration script to create backup
            const result = await this.runCommand('node', args);
            
            // Find backup file path from output
            const backupMatch = result.stdout.match(/Backup created: (.+)/);
            if (backupMatch) {
                this.results.backupPath = backupMatch[1];
                this.log(`✅ Backup created: ${this.results.backupPath}`);
            }

            step.success = true;

        } catch (error) {
            step.error = error.message;
            this.log(`❌ Backup creation failed: ${error.message}`, 'ERROR');
            if (!this.force) {
                throw error;
            }
        } finally {
            step.duration = Date.now() - step.startTime;
            this.results.steps.push(step);
        }

        return this.results.backupPath;
    }

    async runOptimization() {
        this.log('=== RUNNING DATABASE OPTIMIZATION ===');
        const step = { name: 'optimization', startTime: Date.now(), success: false };

        try {
            const args = ['scripts/optimize-database.js'];
            if (this.dryRun) args.push('--dry-run');
            if (this.verbose) args.push('--verbose');

            const result = await this.runCommand('node', args);
            
            // Extract optimization results
            const reportMatch = result.stdout.match(/Optimization report saved: (.+)/);
            if (reportMatch) {
                this.results.reportPath = reportMatch[1];
            }

            step.success = true;
            this.log('✅ Database optimization completed');

        } catch (error) {
            step.error = error.message;
            this.log(`❌ Database optimization failed: ${error.message}`, 'ERROR');
            throw error;
        } finally {
            step.duration = Date.now() - step.startTime;
            this.results.steps.push(step);
        }
    }

    async setupPerformanceMonitoring() {
        this.log('=== SETTING UP PERFORMANCE MONITORING ===');
        const step = { name: 'monitoring', startTime: Date.now(), success: false };

        try {
            // Create monitoring configuration
            const monitoringConfig = {
                enabled: true,
                logInterval: 60000, // 1 minute
                slowQueryThreshold: 1000, // 1 second
                cacheHitRateThreshold: 70, // 70%
                memoryThreshold: 400, // 400MB
                logFilePath: './logs/performance.log',
                alertsEnabled: true
            };

            const configPath = join(__dirname, '..', 'config', 'monitoring.json');
            await fs.mkdir(join(__dirname, '..', 'config'), { recursive: true });
            await fs.writeFile(configPath, JSON.stringify(monitoringConfig, null, 2));

            // Create logs directory
            await fs.mkdir(join(__dirname, '..', 'logs'), { recursive: true });

            step.success = true;
            this.log('✅ Performance monitoring configured');

        } catch (error) {
            step.error = error.message;
            this.log(`❌ Performance monitoring setup failed: ${error.message}`, 'ERROR');
            // Don't throw - this is not critical
        } finally {
            step.duration = Date.now() - step.startTime;
            this.results.steps.push(step);
        }
    }

    async validateOptimization() {
        this.log('=== VALIDATING OPTIMIZATION ===');
        const step = { name: 'validation', startTime: Date.now(), success: false };

        try {
            // Run a simple validation script
            const validationScript = `
                import mongoose from 'mongoose';
                import dotenv from 'dotenv';
                dotenv.config();
                
                try {
                    await mongoose.connect(process.env.MONGODB_URI);
                    const db = mongoose.connection.db;
                    
                    // Check collections exist
                    const collections = await db.listCollections().toArray();
                    const collectionNames = collections.map(c => c.name);
                    
                    const requiredCollections = ['products', 'stores', 'orders', 'users', 'stock_items'];
                    const missingCollections = requiredCollections.filter(name => !collectionNames.includes(name));
                    
                    if (missingCollections.length > 0) {
                        throw new Error('Missing required collections: ' + missingCollections.join(', '));
                    }
                    
                    // Check indexes exist
                    const productIndexes = await db.collection('products').indexes();
                    const hasOptimizedIndexes = productIndexes.some(idx => idx.name.includes('store_active_featured'));
                    
                    console.log('Validation results:');
                    console.log('- Collections:', collections.length);
                    console.log('- Required collections present:', requiredCollections.length);
                    console.log('- Optimized indexes:', hasOptimizedIndexes ? 'Yes' : 'No');
                    
                    await mongoose.disconnect();
                    process.exit(0);
                } catch (error) {
                    console.error('Validation failed:', error.message);
                    process.exit(1);
                }
            `;

            await this.runCommand('node', ['-e', validationScript]);

            step.success = true;
            this.log('✅ Optimization validation passed');

        } catch (error) {
            step.error = error.message;
            this.log(`❌ Optimization validation failed: ${error.message}`, 'ERROR');
            throw error;
        } finally {
            step.duration = Date.now() - step.startTime;
            this.results.steps.push(step);
        }
    }

    async generateFinalReport() {
        this.log('=== GENERATING FINAL REPORT ===');

        const totalDuration = Date.now() - this.results.startTime;
        const successfulSteps = this.results.steps.filter(s => s.success).length;
        const totalSteps = this.results.steps.length;

        const report = {
            timestamp: new Date().toISOString(),
            dryRun: this.dryRun,
            totalDuration,
            success: this.results.success,
            steps: this.results.steps,
            summary: {
                totalSteps,
                successfulSteps,
                failedSteps: totalSteps - successfulSteps,
                backupCreated: !!this.results.backupPath,
                backupPath: this.results.backupPath,
                optimizationReportPath: this.results.reportPath
            },
            recommendations: [
                'Monitor performance metrics regularly using the performance monitor',
                'Review slow queries weekly and optimize as needed',
                'Keep cache hit rates above 70% for optimal performance',
                'Run database optimization monthly or when performance degrades',
                'Backup database before any major changes'
            ]
        };

        const reportPath = join(__dirname, '..', 'logs', `optimization-summary-${Date.now()}.json`);
        await fs.mkdir(join(__dirname, '..', 'logs'), { recursive: true });
        await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

        this.log(`Final report saved: ${reportPath}`);
        this.log(`\n=== OPTIMIZATION SUMMARY ===`);
        this.log(`Total time: ${Math.round(totalDuration / 1000)}s`);
        this.log(`Steps completed: ${successfulSteps}/${totalSteps}`);
        this.log(`Success rate: ${Math.round((successfulSteps / totalSteps) * 100)}%`);
        
        if (this.results.backupPath) {
            this.log(`Backup: ${this.results.backupPath}`);
        }
        
        if (this.results.reportPath) {
            this.log(`Optimization report: ${this.results.reportPath}`);
        }

        return report;
    }

    async run() {
        try {
            this.log('🚀 Starting comprehensive database optimization...');
            
            if (this.dryRun) {
                this.log('Running in DRY RUN mode - no permanent changes will be made');
            }

            // Run optimization steps
            await this.checkPrerequisites();
            await this.createBackup();
            await this.runOptimization();
            await this.setupPerformanceMonitoring();
            await this.validateOptimization();

            this.results.success = true;
            this.log('✅ Database optimization completed successfully!');

        } catch (error) {
            this.results.success = false;
            this.log(`❌ Database optimization failed: ${error.message}`, 'ERROR');
            
            if (this.results.backupPath) {
                this.log(`💾 Backup available for rollback: ${this.results.backupPath}`, 'INFO');
            }
        } finally {
            await this.generateFinalReport();
        }

        return this.results;
    }
}

// Show usage if no arguments
if (process.argv.length === 2) {
    console.log(`
Database Optimization Runner

Usage: node scripts/run-optimization.js [options]

Options:
  --dry-run        Preview changes without making them
  --skip-backup    Skip backup creation (not recommended)
  --verbose        Show detailed output
  --force          Continue even if backup fails

Examples:
  node scripts/run-optimization.js --dry-run --verbose
  node scripts/run-optimization.js
  node scripts/run-optimization.js --skip-backup --force
`);
    process.exit(0);
}

// Run the optimization
const runner = new OptimizationRunner();
runner.run().catch(console.error);