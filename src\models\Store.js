import mongoose from 'mongoose';

/**
 * Schema para lojas do Discord
 */
const storeSchema = new mongoose.Schema({
    // Informações básicas da loja
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    description: {
        type: String,
        required: true,
        trim: true,
        maxlength: 1000
    },
    banner: {
        type: String,
        required: true,
        validate: {
            validator: function(v) {
                // Validação básica de URL
                return /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(v);
            },
            message: 'Banner deve ser uma URL válida de imagem'
        }
    },
    color: {
        type: String,
        required: true,
        validate: {
            validator: function(v) {
                // Aceita códigos hex (#FFFFFF) ou nomes de cores
                return /^#[0-9A-F]{6}$/i.test(v) || /^[a-zA-Z]+$/i.test(v);
            },
            message: 'Cor deve ser um código hexadecimal válido (#FFFFFF) ou nome de cor'
        }
    },
    
    // Informações do Discord
    guildId: {
        type: String,
        required: true
    },
    channelId: {
        type: String,
        required: true
    },
    messageId: {
        type: String, // ID da mensagem do embed principal
        default: null
    },
    
    // Configurações da loja
    isActive: {
        type: Boolean,
        default: true
    },
    allowedRoles: [{
        type: String // IDs dos cargos que podem ver a loja
    }],
    
    // Metadados
    createdBy: {
        type: String, // Discord ID do dono do bot que criou
        required: true
    },
    lastModifiedBy: {
        type: String // Discord ID do admin que modificou
    }
}, {
    timestamps: true,
    collection: 'stores'
});

// Índices para otimização
storeSchema.index({ guildId: 1 });
storeSchema.index({ channelId: 1 });
storeSchema.index({ isActive: 1 });

// Middleware para conversão de cor
storeSchema.pre('save', function(next) {
    // Converte nomes de cores para códigos hex se necessário
    const colorMap = {
        'red': '#FF0000',
        'green': '#00FF00',
        'blue': '#0000FF',
        'yellow': '#FFFF00',
        'purple': '#800080',
        'orange': '#FFA500',
        'pink': '#FFC0CB',
        'black': '#000000',
        'white': '#FFFFFF',
        'gray': '#808080',
        'grey': '#808080'
    };
    
    if (this.color && !this.color.startsWith('#')) {
        const lowerColor = this.color.toLowerCase();
        if (colorMap[lowerColor]) {
            this.color = colorMap[lowerColor];
        }
    }
    
    next();
});

// Métodos do schema
storeSchema.methods.getColorAsInt = function() {
    // Converte cor hex para inteiro para uso no Discord.js
    return parseInt(this.color.replace('#', ''), 16);
};

storeSchema.methods.isUserAllowed = function(userId, userRoles = []) {
    if (!this.isActive) return false;
    
    // Se não há roles específicas, qualquer um pode ver
    if (this.allowedRoles.length === 0) return true;
    
    // Verifica se o usuário tem algum dos roles permitidos
    return this.allowedRoles.some(roleId => userRoles.includes(roleId));
};

// Métodos estáticos
storeSchema.statics.findByGuild = function(guildId) {
    return this.find({ guildId, isActive: true });
};

storeSchema.statics.findByChannel = function(channelId) {
    return this.findOne({ channelId, isActive: true });
};

export default mongoose.model('Store', storeSchema);
