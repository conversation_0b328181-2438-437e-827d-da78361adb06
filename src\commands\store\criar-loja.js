import { SlashCommandBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } from 'discord.js';
import { logger } from '../../utils/logger.js';

export default {
    data: new SlashCommandBuilder()
        .setName('criar-loja')
        .setDescription('Cria uma nova loja no servidor (apenas administradores)'),
    
    async execute(interaction) {
        try {
            // Log do início da execução do comando
            await logger.userAction('Tentativa de criação de loja', {
                guildId: interaction.guild.id,
                userId: interaction.user.id,
                command: 'criar-loja'
            }, {
                user: interaction.user.tag,
                guild: interaction.guild.name,
                isAdmin: interaction.member.permissions.has('Administrator')
            });

            // Verificação se o usuário é administrador
            if (!interaction.member.permissions.has('Administrator')) {
                await logger.security('Tentativa de uso de comando admin por usuário não autorizado', {
                    guildId: interaction.guild.id,
                    userId: interaction.user.id,
                    command: 'criar-loja'
                }, {
                    user: interaction.user.tag,
                    permissions: interaction.member.permissions.toArray()
                });

                return await interaction.reply({
                    content: '❌ Apenas administradores podem executar este comando.',
                    ephemeral: true
                });
            }

            // Verificação se o bot tem permissões necessárias
            const botMember = interaction.guild.members.me;
            const requiredPermissions = ['ManageChannels', 'ManageRoles', 'SendMessages', 'EmbedLinks'];
            
            const missingPermissions = requiredPermissions.filter(perm => 
                !botMember.permissions.has(perm)
            );

            if (missingPermissions.length > 0) {
                return await interaction.reply({
                    content: `❌ O bot precisa das seguintes permissões: ${missingPermissions.join(', ')}`,
                    ephemeral: true
                });
            }

            // Criação do modal
            const modal = new ModalBuilder()
                .setCustomId('store_create')
                .setTitle('Criar Nova Loja');

            // Campo para banner da loja
            const bannerInput = new TextInputBuilder()
                .setCustomId('store_banner')
                .setLabel('Banner da Loja (URL da imagem)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://exemplo.com/banner.png')
                .setRequired(true)
                .setMaxLength(500);

            // Campo para nome da loja
            const nameInput = new TextInputBuilder()
                .setCustomId('store_name')
                .setLabel('Nome da Loja')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Digite o nome da sua loja')
                .setRequired(true)
                .setMinLength(3)
                .setMaxLength(100);

            // Campo para cor da loja
            const colorInput = new TextInputBuilder()
                .setCustomId('store_color')
                .setLabel('Cor da Loja (hex ou nome)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('#FF0000 ou red')
                .setRequired(true)
                .setMaxLength(20);

            // Campo para descrição da loja
            const descriptionInput = new TextInputBuilder()
                .setCustomId('store_description')
                .setLabel('Descrição da Loja')
                .setStyle(TextInputStyle.Paragraph)
                .setPlaceholder('Descreva sua loja e os produtos que oferece')
                .setRequired(true)
                .setMinLength(10)
                .setMaxLength(1000);

            // Criação das action rows
            const bannerRow = new ActionRowBuilder().addComponents(bannerInput);
            const nameRow = new ActionRowBuilder().addComponents(nameInput);
            const colorRow = new ActionRowBuilder().addComponents(colorInput);
            const descriptionRow = new ActionRowBuilder().addComponents(descriptionInput);

            // Adiciona os componentes ao modal
            modal.addComponents(bannerRow, nameRow, colorRow, descriptionRow);

            // Exibe o modal
            await interaction.showModal(modal);

            await logger.userAction('Modal de criação de loja exibido', {
                guildId: interaction.guild.id,
                userId: interaction.user.id,
                command: 'criar-loja'
            }, {
                user: interaction.user.tag,
                guild: interaction.guild.name,
                modalId: 'store_create'
            });

        } catch (error) {
            await logger.logStructured('ERROR', 'COMMAND', 'Erro ao executar comando criar-loja', {
                guildId: interaction.guild.id,
                userId: interaction.user.id,
                command: 'criar-loja'
            }, {
                error: error.message,
                stack: error.stack,
                user: interaction.user.tag
            });

            const errorMessage = {
                content: '❌ Erro ao abrir o formulário de criação de loja.',
                ephemeral: true
            };

            try {
                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp(errorMessage);
                } else {
                    await interaction.reply(errorMessage);
                }
            } catch (replyError) {
                await logger.logStructured('ERROR', 'COMMAND', 'Erro ao responder após falha no comando', {
                    guildId: interaction.guild.id,
                    userId: interaction.user.id,
                    command: 'criar-loja'
                }, {
                    originalError: error.message,
                    replyError: replyError.message
                });
            }
        }
    }
};
