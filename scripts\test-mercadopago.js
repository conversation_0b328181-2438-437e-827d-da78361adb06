import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { pixPaymentManager } from '../src/utils/pixPaymentManager.js';
import { logger } from '../src/utils/logger.js';
import BotConfig from '../src/models/BotConfig.js';

// Carrega variáveis de ambiente
dotenv.config();

/**
 * Script para testar a configuração do MercadoPago
 */
async function testMercadoPagoConfiguration() {
    try {
        console.log('🔄 Iniciando teste da configuração do MercadoPago...');
        
        // Conecta ao banco de dados
        if (!process.env.MONGODB_URI) {
            throw new Error('MONGODB_URI não está definida no arquivo .env');
        }

        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB');

        // Lista todas as configurações de guild
        const configs = await BotConfig.find({ 'mercadoPago.isEnabled': true });
        
        if (configs.length === 0) {
            console.log('❌ Nenhuma configuração do MercadoPago encontrada');
            console.log('💡 Configure o MercadoPago usando o comando /configbot no Discord');
            return;
        }

        console.log(`📋 Encontradas ${configs.length} configuração(ões) do MercadoPago:`);

        for (const config of configs) {
            console.log(`\n🏪 Testando Guild: ${config.guildId}`);
            
            // Testa conectividade
            const testResult = await pixPaymentManager.testApiConnection(config.guildId);
            
            if (testResult.success) {
                console.log(`✅ Conectividade: OK`);
                console.log(`🌍 Ambiente: ${testResult.environment}`);
                console.log(`📡 Status: ${testResult.details?.status}`);
                
                // Testa criação de pagamento de teste (apenas em sandbox)
                if (testResult.environment === 'sandbox') {
                    console.log('🧪 Testando criação de pagamento PIX...');
                    
                    try {
                        const testPayment = await pixPaymentManager.createPixPayment({
                            amount: 1.00,
                            description: 'Teste de configuração',
                            payerName: 'Teste',
                            payerEmail: '<EMAIL>',
                            externalReference: `test_${Date.now()}`
                        }, config.guildId);

                        console.log(`✅ Pagamento de teste criado: ${testPayment.id}`);
                        console.log(`📱 QR Code gerado: ${testPayment.debug?.hasQRCode ? 'Sim' : 'Não'}`);
                        console.log(`✔️ QR Code válido: ${testPayment.debug?.qrCodeValid ? 'Sim' : 'Não'}`);
                        
                        // Testa verificação de status
                        const statusResult = await pixPaymentManager.checkPaymentStatus(testPayment.id, config.guildId);
                        console.log(`📊 Status do pagamento: ${statusResult.status} (${statusResult.statusDetail})`);
                        
                    } catch (paymentError) {
                        console.log(`❌ Erro ao criar pagamento de teste: ${paymentError.message}`);
                    }
                } else {
                    console.log('⚠️ Ambiente de produção - pulando teste de criação de pagamento');
                }
                
            } else {
                console.log(`❌ Conectividade: FALHA`);
                console.log(`🚫 Erro: ${testResult.error}`);
                console.log(`📋 Detalhes: ${testResult.details}`);
            }
        }

        console.log('\n🎉 Teste concluído!');

    } catch (error) {
        console.error('❌ Erro durante o teste:', error.message);
        console.error('Stack trace:', error.stack);
    } finally {
        await mongoose.disconnect();
        console.log('✅ Desconectado do MongoDB');
    }
}

/**
 * Função para validar tokens manualmente
 */
async function validateTokens() {
    const accessToken = process.argv[3];
    const publicKey = process.argv[4];

    if (!accessToken || !publicKey) {
        console.log('❌ Uso: npm run test:mercadopago validate <access_token> <public_key>');
        return;
    }

    console.log('🔍 Validando tokens...');
    
    // Detecta ambiente
    const isAccessTokenSandbox = accessToken.startsWith('TEST-');
    const isAccessTokenProduction = accessToken.startsWith('APP_USR-');
    const isPublicKeySandbox = publicKey.startsWith('TEST-');
    const isPublicKeyProduction = publicKey.startsWith('APP_USR-');

    console.log(`🔑 Access Token: ${accessToken.substring(0, 20)}...`);
    console.log(`🔓 Public Key: ${publicKey.substring(0, 20)}...`);

    // Validações
    if (!isAccessTokenSandbox && !isAccessTokenProduction) {
        console.log('❌ Access Token inválido - deve começar com TEST- ou APP_USR-');
        return;
    }

    if (!isPublicKeySandbox && !isPublicKeyProduction) {
        console.log('❌ Public Key inválido - deve começar com TEST- ou APP_USR-');
        return;
    }

    if ((isAccessTokenSandbox && !isPublicKeySandbox) || (isAccessTokenProduction && !isPublicKeyProduction)) {
        console.log('❌ Tokens de ambientes diferentes - devem ser ambos sandbox ou ambos produção');
        return;
    }

    const environment = isAccessTokenSandbox ? 'sandbox' : 'production';
    console.log(`✅ Tokens válidos para ambiente: ${environment}`);

    // Verifica ambiente vs NODE_ENV
    const nodeEnv = process.env.NODE_ENV || 'development';
    if (nodeEnv === 'production' && environment === 'sandbox') {
        console.log('⚠️ AVISO: Usando tokens de sandbox em ambiente de produção');
    } else if (nodeEnv !== 'production' && environment === 'production') {
        console.log('⚠️ AVISO: Usando tokens de produção em ambiente de desenvolvimento');
    }
}

// Executa o script
const command = process.argv[2];

if (command === 'validate') {
    validateTokens();
} else {
    testMercadoPagoConfiguration();
}
