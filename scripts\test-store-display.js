import dotenv from 'dotenv';
import mongoose from 'mongoose';

// Carrega variáveis de ambiente
dotenv.config();

async function testStoreDisplay() {
    try {
        console.log('🧪 Testando lógica de exibição da loja...');
        
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB');
        
        const db = mongoose.connection.db;
        const productsCollection = db.collection('products');
        const stockItemsCollection = db.collection('stock_items');
        const storesCollection = db.collection('stores');
        
        // 1. Simula a lógica do storeManager.js
        console.log('\n🏪 SIMULANDO LÓGICA DE EXIBIÇÃO DA LOJA:');
        console.log('=' .repeat(60));
        
        // Busca lojas do servidor (usando um guildId conhecido)
        const guildId = '1396705329991127151'; // Do arquivo .env
        const stores = await storesCollection.find({ 
            guildId: guildId,
            isActive: { $ne: false }
        }).toArray();
        
        console.log(`📋 Lojas encontradas: ${stores.length}`);
        stores.forEach(store => {
            console.log(`   - ${store.name} (ID: ${store._id})`);
        });
        
        if (stores.length === 0) {
            console.log('❌ Nenhuma loja encontrada para o servidor');
            return;
        }
        
        // Busca produtos ativos das lojas do servidor (linha 224-227 do storeManager.js)
        const storeIds = stores.map(store => store._id);
        const products = await productsCollection.find({ 
            storeId: { $in: storeIds },
            status: 'active'
        }).limit(20).toArray();
        
        console.log(`\n📦 Produtos com status 'active': ${products.length}`);
        products.forEach(product => {
            console.log(`   - ${product.name} (Status: ${product.status})`);
        });
        
        // Filtra produtos que têm estoque disponível (linha 229-236 do storeManager.js)
        console.log('\n📊 VERIFICANDO ESTOQUE DISPONÍVEL:');
        console.log('=' .repeat(60));
        
        const productsWithStock = [];
        for (const product of products) {
            const availableStock = await stockItemsCollection.countDocuments({
                productId: product._id,
                status: 'available'
            });
            
            console.log(`📦 ${product.name}:`);
            console.log(`   - Estoque disponível: ${availableStock}`);
            console.log(`   - Status: ${product.status}`);
            
            if (availableStock > 0) {
                productsWithStock.push(product);
                console.log(`   ✅ Será exibido na loja`);
            } else {
                console.log(`   ❌ NÃO será exibido (sem estoque)`);
            }
        }
        
        console.log(`\n🎯 RESULTADO FINAL:`);
        console.log('=' .repeat(60));
        console.log(`✅ ${productsWithStock.length} produto(s) será(ão) exibido(s) na loja:`);
        
        productsWithStock.forEach((product, index) => {
            console.log(`   ${index + 1}. ${product.name}`);
            console.log(`      - Preço: R$ ${product.price}`);
            console.log(`      - Descrição: ${product.description.substring(0, 50)}...`);
            console.log(`      - Emoji: ${product.emoji || 'Nenhum'}`);
        });
        
        // 2. Testa especificamente o produto "Teste"
        console.log(`\n🔍 VERIFICAÇÃO ESPECÍFICA DO PRODUTO "TESTE":`);
        console.log('=' .repeat(60));
        
        const testeProduct = await productsCollection.findOne({ 
            name: { $regex: /^teste$/i }
        });
        
        if (testeProduct) {
            console.log(`✅ Produto "Teste" encontrado:`);
            console.log(`   - ID: ${testeProduct._id}`);
            console.log(`   - Status: ${testeProduct.status}`);
            console.log(`   - StoreId: ${testeProduct.storeId}`);
            
            const testeStock = await stockItemsCollection.countDocuments({
                productId: testeProduct._id,
                status: 'available'
            });
            
            console.log(`   - Estoque disponível: ${testeStock}`);
            
            // Verifica se está na lista de produtos com estoque
            const isInList = productsWithStock.some(p => p._id.toString() === testeProduct._id.toString());
            console.log(`   - Aparece na loja: ${isInList ? '✅ SIM' : '❌ NÃO'}`);
            
            if (!isInList) {
                console.log(`   ⚠️  Motivos possíveis:`);
                if (testeProduct.status !== 'active') {
                    console.log(`      - Status não é 'active' (atual: '${testeProduct.status}')`);
                }
                if (testeStock === 0) {
                    console.log(`      - Sem estoque disponível`);
                }
                if (!storeIds.some(id => id.toString() === testeProduct.storeId.toString())) {
                    console.log(`      - Loja não pertence ao servidor`);
                }
            }
        } else {
            console.log(`❌ Produto "Teste" não encontrado`);
        }
        
        // 3. Simula a criação do select menu (linha 238-264 do storeManager.js)
        console.log(`\n🎛️ SIMULANDO SELECT MENU:`);
        console.log('=' .repeat(60));
        
        if (productsWithStock.length === 0) {
            console.log('📭 Select menu seria desabilitado com mensagem "Não há produtos"');
        } else {
            console.log('✅ Select menu seria habilitado com as seguintes opções:');
            productsWithStock.forEach((product, index) => {
                const label = product.name.substring(0, 100);
                const description = product.description.substring(0, 100);
                const emoji = product.isDigital ? '💾' : '📦';
                
                console.log(`   ${index + 1}. ${emoji} ${label}`);
                console.log(`      "${description}"`);
                console.log(`      Valor: ${product._id}`);
            });
        }
        
        await mongoose.disconnect();
        console.log('\n✅ Teste de exibição da loja concluído!');
        
    } catch (error) {
        console.error('❌ Erro durante teste:', error);
        process.exit(1);
    }
}

testStoreDisplay();
