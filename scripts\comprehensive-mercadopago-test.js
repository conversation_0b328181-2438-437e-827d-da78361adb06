import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { pixPaymentManager } from '../src/utils/pixPaymentManager.js';
import { logger } from '../src/utils/logger.js';
import BotConfig from '../src/models/BotConfig.js';

// Carrega variáveis de ambiente
dotenv.config();

/**
 * Script abrangente para testar toda a configuração do MercadoPago
 */
async function comprehensiveMercadoPagoTest() {
    try {
        console.log('🚀 Iniciando teste abrangente do MercadoPago...\n');
        
        // Conecta ao banco de dados
        if (!process.env.MONGODB_URI) {
            throw new Error('MONGODB_URI não está definida no arquivo .env');
        }

        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB\n');

        // 1. Verifica configurações no banco
        console.log('📋 ETAPA 1: Verificando configurações no banco de dados');
        console.log('─'.repeat(60));
        
        const configs = await BotConfig.find({ 'mercadoPago.isEnabled': true });
        
        if (configs.length === 0) {
            console.log('❌ Nenhuma configuração do MercadoPago encontrada');
            console.log('💡 Configure o MercadoPago usando o comando /configbot no Discord\n');
            return;
        }

        console.log(`✅ Encontradas ${configs.length} configuração(ões) ativas\n`);

        for (const config of configs) {
            console.log(`🏪 Guild ID: ${config.guildId}`);
            
            // Verifica integridade dos dados
            const hasAccessToken = !!config.mercadoPago?.accessToken;
            const hasPublicKey = !!config.mercadoPago?.publicKey;
            const isEnabled = config.mercadoPago?.isEnabled;
            
            console.log(`   🔑 Access Token: ${hasAccessToken ? '✅ Presente' : '❌ Ausente'}`);
            console.log(`   🔓 Public Key: ${hasPublicKey ? '✅ Presente' : '❌ Ausente'}`);
            console.log(`   ⚡ Habilitado: ${isEnabled ? '✅ Sim' : '❌ Não'}`);
            
            if (!hasAccessToken || !hasPublicKey || !isEnabled) {
                console.log('   ⚠️ Configuração incompleta - pulando testes de API\n');
                continue;
            }

            // Detecta ambiente
            const isSandbox = config.mercadoPago.accessToken.startsWith('TEST-');
            const environment = isSandbox ? 'sandbox' : 'production';
            console.log(`   🌍 Ambiente: ${environment}\n`);

            // 2. Testa conectividade
            console.log('📡 ETAPA 2: Testando conectividade com API');
            console.log('─'.repeat(60));
            
            const connectivityResult = await pixPaymentManager.testApiConnection(config.guildId);
            
            if (connectivityResult.success) {
                console.log('✅ Conectividade: SUCESSO');
                console.log(`   📊 Status: ${connectivityResult.details?.status}`);
                console.log(`   🔧 Método: ${connectivityResult.details?.method}`);
                console.log(`   🎯 Resultado: ${connectivityResult.details?.testResult || 'API respondendo'}`);
            } else {
                console.log('❌ Conectividade: FALHA');
                console.log(`   🚫 Erro: ${connectivityResult.error}`);
                console.log(`   📋 Detalhes: ${connectivityResult.details}`);
                
                if (connectivityResult.debugInfo) {
                    console.log(`   🔍 Debug: ${JSON.stringify(connectivityResult.debugInfo, null, 2)}`);
                }
                
                console.log('   ⚠️ Pulando testes adicionais devido ao erro de conectividade\n');
                continue;
            }

            // 3. Testa criação de pagamento (apenas em sandbox)
            if (environment === 'sandbox') {
                console.log('\n🧪 ETAPA 3: Testando criação de pagamento PIX (Sandbox)');
                console.log('─'.repeat(60));
                
                try {
                    const testPayment = await pixPaymentManager.createPixPayment({
                        amount: 1.00,
                        description: 'Teste abrangente de configuração',
                        payerName: 'Teste Automatizado',
                        payerEmail: '<EMAIL>',
                        externalReference: `comprehensive_test_${Date.now()}`
                    }, config.guildId);

                    console.log('✅ Pagamento criado com sucesso');
                    console.log(`   🆔 ID: ${testPayment.id}`);
                    console.log(`   💰 Valor: R$ ${testPayment.amount}`);
                    console.log(`   📱 QR Code: ${testPayment.debug?.hasQRCode ? '✅ Gerado' : '❌ Não gerado'}`);
                    console.log(`   ✔️ QR Válido: ${testPayment.debug?.qrCodeValid ? '✅ Sim' : '❌ Não'}`);
                    console.log(`   🌍 Ambiente: ${testPayment.debug?.environment}`);
                    
                    // 4. Testa verificação de status
                    console.log('\n📊 ETAPA 4: Testando verificação de status');
                    console.log('─'.repeat(60));
                    
                    const statusResult = await pixPaymentManager.checkPaymentStatus(testPayment.id, config.guildId);
                    console.log(`✅ Status verificado: ${statusResult.status}`);
                    console.log(`   📋 Detalhes: ${statusResult.statusDetail}`);
                    console.log(`   ⏳ Pendente: ${statusResult.pending ? 'Sim' : 'Não'}`);
                    
                    // 5. Testa informações de debug
                    console.log('\n🔍 ETAPA 5: Testando informações de debug');
                    console.log('─'.repeat(60));
                    
                    const debugInfo = await pixPaymentManager.getPaymentDebugInfo(testPayment.id, config.guildId);
                    if (debugInfo.error) {
                        console.log(`❌ Erro ao obter debug: ${debugInfo.error}`);
                    } else {
                        console.log('✅ Informações de debug obtidas');
                        console.log(`   🆔 ID: ${debugInfo.id}`);
                        console.log(`   📊 Status: ${debugInfo.status}`);
                        console.log(`   💰 Valor: R$ ${debugInfo.transactionAmount}`);
                        console.log(`   📱 QR Code válido: ${debugInfo.qrCodeData?.isValidQRCode ? 'Sim' : 'Não'}`);
                    }
                    
                } catch (paymentError) {
                    console.log('❌ Erro ao criar pagamento de teste');
                    console.log(`   🚫 Erro: ${paymentError.message}`);
                    console.log(`   📋 Stack: ${paymentError.stack}`);
                }
                
            } else {
                console.log('\n⚠️ ETAPA 3-5: Pulando testes de pagamento (ambiente de produção)');
            }

            console.log('\n' + '═'.repeat(80) + '\n');
        }

        // 6. Resumo final
        console.log('📊 RESUMO FINAL');
        console.log('─'.repeat(60));
        console.log(`✅ Configurações testadas: ${configs.length}`);
        console.log('💡 Recomendações:');
        console.log('   - Use tokens TEST- para desenvolvimento');
        console.log('   - Use tokens APP_USR- apenas em produção');
        console.log('   - Monitore os logs para identificar problemas');
        console.log('   - Teste regularmente a conectividade');

    } catch (error) {
        console.error('❌ Erro durante o teste abrangente:', error.message);
        console.error('Stack trace:', error.stack);
    } finally {
        await mongoose.disconnect();
        console.log('\n✅ Desconectado do MongoDB');
        console.log('🎉 Teste abrangente concluído!');
    }
}

/**
 * Função para testar configuração específica
 */
async function testSpecificGuild() {
    const guildId = process.argv[3];
    
    if (!guildId) {
        console.log('❌ Uso: npm run test:mercadopago-full specific <guild_id>');
        return;
    }

    try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log(`🔍 Testando configuração específica para Guild: ${guildId}\n`);
        
        const config = await BotConfig.findByGuild(guildId);
        if (!config || !config.mercadoPago?.isEnabled) {
            console.log('❌ Configuração não encontrada ou não habilitada para esta guild');
            return;
        }

        // Executa teste de conectividade
        const result = await pixPaymentManager.testApiConnection(guildId);
        
        console.log('📊 Resultado do teste:');
        console.log(JSON.stringify(result, null, 2));
        
    } catch (error) {
        console.error('❌ Erro:', error.message);
    } finally {
        await mongoose.disconnect();
    }
}

// Executa o script
const command = process.argv[2];

if (command === 'specific') {
    testSpecificGuild();
} else {
    comprehensiveMercadoPagoTest();
}
