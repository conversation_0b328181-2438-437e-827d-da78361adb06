import dotenv from 'dotenv';
import mongoose from 'mongoose';

// Carrega variáveis de ambiente
dotenv.config();

async function fixProductStatus() {
    try {
        console.log('🔧 Corrigindo status de produtos com valores inválidos...');
        
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB');
        
        const db = mongoose.connection.db;
        const productsCollection = db.collection('products');
        const stockItemsCollection = db.collection('stock_items');
        
        // 1. Busca produtos com status inválidos
        console.log('\n🔍 Buscando produtos com status inválidos...');
        
        const validStatuses = ['active', 'inactive', 'out_of_stock', 'discontinued'];
        const invalidProducts = await productsCollection.find({
            status: { $nin: validStatuses }
        }).toArray();
        
        console.log(`📋 Encontrados ${invalidProducts.length} produtos com status inválidos:`);
        
        for (const product of invalidProducts) {
            console.log(`\n📦 Produto: ${product.name}`);
            console.log(`   ID: ${product._id}`);
            console.log(`   Status atual: "${product.status}"`);
            
            // Verifica se tem estoque disponível
            const availableStock = await stockItemsCollection.countDocuments({
                productId: product._id,
                status: 'available'
            });
            
            console.log(`   Estoque disponível: ${availableStock}`);
            
            // Determina o status correto baseado no estoque
            let newStatus;
            if (product.status === 'in_stock' || product.status === 'available') {
                // Se tinha status indicando "em estoque", verifica se realmente tem estoque
                newStatus = availableStock > 0 ? 'active' : 'out_of_stock';
            } else if (product.status === 'no_stock' || product.status === 'empty') {
                // Se tinha status indicando "sem estoque"
                newStatus = availableStock > 0 ? 'active' : 'out_of_stock';
            } else {
                // Para outros status inválidos, usa lógica baseada no estoque
                newStatus = availableStock > 0 ? 'active' : 'out_of_stock';
            }
            
            console.log(`   Novo status: "${newStatus}"`);
            
            // Atualiza o produto
            const updateResult = await productsCollection.updateOne(
                { _id: product._id },
                { 
                    $set: { 
                        status: newStatus,
                        updatedAt: new Date()
                    }
                }
            );
            
            if (updateResult.modifiedCount > 0) {
                console.log(`   ✅ Status atualizado com sucesso`);
            } else {
                console.log(`   ❌ Falha ao atualizar status`);
            }
        }
        
        // 2. Verifica se há outros problemas de dados
        console.log('\n🔍 Verificando outros problemas de dados...');
        
        // Produtos sem storeId
        const productsWithoutStore = await productsCollection.countDocuments({
            $or: [
                { storeId: { $exists: false } },
                { storeId: null }
            ]
        });
        
        if (productsWithoutStore > 0) {
            console.log(`⚠️  ${productsWithoutStore} produtos sem storeId definido`);
        }
        
        // Produtos sem createdBy
        const productsWithoutCreator = await productsCollection.countDocuments({
            $or: [
                { createdBy: { $exists: false } },
                { createdBy: null },
                { createdBy: '' }
            ]
        });
        
        if (productsWithoutCreator > 0) {
            console.log(`⚠️  ${productsWithoutCreator} produtos sem createdBy definido`);
        }
        
        // 3. Relatório final
        console.log('\n📊 RELATÓRIO DE CORREÇÃO:');
        console.log('=' .repeat(50));
        
        if (invalidProducts.length === 0) {
            console.log('✅ Todos os produtos já tinham status válidos');
        } else {
            console.log(`✅ ${invalidProducts.length} produto(s) corrigido(s)`);
            
            // Verifica produtos que agora devem aparecer na loja
            const nowActiveProducts = invalidProducts.filter(p => {
                // Simula a lógica de determinação do novo status
                return p.status === 'in_stock' || p.status === 'available';
            });
            
            if (nowActiveProducts.length > 0) {
                console.log(`🎉 ${nowActiveProducts.length} produto(s) agora deve(m) aparecer na loja:`);
                nowActiveProducts.forEach(p => {
                    console.log(`   - ${p.name}`);
                });
            }
        }
        
        // 4. Lista produtos ativos finais
        console.log('\n📋 Produtos ativos após correção:');
        const activeProducts = await productsCollection.find({
            status: 'active'
        }).toArray();
        
        console.log(`✅ ${activeProducts.length} produto(s) ativo(s):`);
        for (const product of activeProducts) {
            const stockCount = await stockItemsCollection.countDocuments({
                productId: product._id,
                status: 'available'
            });
            console.log(`   - ${product.name} (${stockCount} itens em estoque)`);
        }
        
        await mongoose.disconnect();
        console.log('\n✅ Correção concluída com sucesso!');
        
    } catch (error) {
        console.error('❌ Erro durante correção:', error);
        process.exit(1);
    }
}

fixProductStatus();
