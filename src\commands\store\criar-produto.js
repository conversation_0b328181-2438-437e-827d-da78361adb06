import { SlashCommandBuilder, StringSelectMenuBuilder, StringSelectMenuOptionBuilder, ActionRowBuilder } from 'discord.js';
import { logger } from '../../utils/logger.js';
import Store from '../../models/Store.js';

export default {
    data: new SlashCommandBuilder()
        .setName('criar-produto')
        .setDescription('Cria um novo produto para uma loja existente (apenas administradores)'),
    
    async execute(interaction) {
        try {
            // Verificação se o usuário é administrador
            if (!interaction.member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem executar este comando.',
                    ephemeral: true
                });
            }

            // Busca lojas ativas no servidor atual
            const stores = await Store.findByGuild(interaction.guild.id);

            if (stores.length === 0) {
                return await interaction.reply({
                    content: '❌ Não há lojas criadas neste servidor. Use `/criar-loja` para criar uma nova loja.',
                    ephemeral: true
                });
            }

            // Cria o select menu com as lojas disponíveis
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('create_product_store_select')
                .setPlaceholder('Qual loja deve receber este produto?')
                .setMinValues(1)
                .setMaxValues(1);

            // Adiciona as lojas como opções
            for (const store of stores) {
                // Busca informações do canal
                const channel = interaction.guild.channels.cache.get(store.channelId);
                const channelInfo = channel ? `#${channel.name}` : `Canal não encontrado`;
                
                selectMenu.addOptions(
                    new StringSelectMenuOptionBuilder()
                        .setLabel(store.name)
                        .setDescription(`${channelInfo} • Criada em ${store.createdAt.toLocaleDateString('pt-BR')}`)
                        .setValue(store._id.toString())
                        .setEmoji('🏪')
                );
            }

            // Adiciona opção de cancelar
            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel('❌ Cancelar')
                    .setDescription('Cancelar a criação do produto')
                    .setValue('cancel')
                    .setEmoji('❌')
            );

            const row = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.reply({
                content: '🏪 **Criar Novo Produto**\n\nSelecione a loja que deve receber o novo produto:',
                components: [row],
                ephemeral: true
            });

            logger.info(`Comando criar-produto executado por ${interaction.user.tag} em ${interaction.guild.name}`);

        } catch (error) {
            logger.error('Erro ao executar comando criar-produto:', error);
            
            const errorMessage = {
                content: '❌ Ocorreu um erro ao carregar as lojas. Tente novamente mais tarde.',
                ephemeral: true
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    }
};
