import { readdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath, pathToFileURL } from 'url';
import { logger } from './logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Carrega todos os eventos da pasta events
 * @param {Client} client - Instância do cliente Discord
 */
export async function loadEvents(client) {
    try {
        const eventsPath = join(__dirname, '..', 'events');
        let eventCount = 0;

        // Lê todos os arquivos de evento
        const eventFiles = readdirSync(eventsPath)
            .filter(file => file.endsWith('.js'));

        for (const file of eventFiles) {
            try {
                const filePath = join(eventsPath, file);
                const fileUrl = pathToFileURL(filePath).href;

                // Importa o evento dinamicamente
                const event = await import(fileUrl);
                
                if (event.default && event.default.name && event.default.execute) {
                    // Registra o evento
                    if (event.default.once) {
                        client.once(event.default.name, (...args) => event.default.execute(...args));
                    } else {
                        client.on(event.default.name, (...args) => event.default.execute(...args));
                    }
                    
                    eventCount++;
                    logger.debug(`Evento carregado: ${event.default.name} (${event.default.once ? 'once' : 'on'})`);
                } else {
                    logger.warn(`⚠️ Evento inválido encontrado: ${file} - faltando propriedades obrigatórias`);
                }
            } catch (error) {
                logger.error(`❌ Erro ao carregar evento ${file}: ${error.message}`);
                console.error(error.stack);
            }
        }

        logger.info(`✅ ${eventCount} eventos carregados com sucesso`);
        
    } catch (error) {
        logger.error('❌ Erro ao carregar eventos:', error);
        throw error;
    }
}
