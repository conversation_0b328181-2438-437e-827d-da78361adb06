import dotenv from 'dotenv';
// Carrega variáveis de ambiente
dotenv.config();

import { CartButtonHandler } from '../src/handlers/cartButtonHandler.js';
import { logger } from '../src/utils/logger.js';
import { connectDatabase } from '../src/database/connection.js';

/**
 * Script para testar o método handleCancelPurchase diretamente
 */
async function testCartCancelPurchase() {
    console.log('🧪 Testando método handleCancelPurchase...');
    
    // Conecta ao banco de dados primeiro
    console.log('🔌 Conectando ao banco de dados...');
    try {
        await connectDatabase();
        console.log('✅ Conectado ao banco de dados');
    } catch (error) {
        console.error('❌ Erro ao conectar ao banco:', error.message);
        process.exit(1);
    }
    
    // Mock mais completo da interação do Discord
    const mockInteraction = {
        customId: 'cart_cancel_purchase',
        user: {
            id: 'test_user_123',
            tag: 'TestUser#1234'
        },
        guild: {
            id: 'test_guild_123'
        },
        reply: async (options) => {
            console.log('📤 Resposta da interação:', JSON.stringify(options, null, 2));
            return { id: 'mock_message_id' };
        },
        followUp: async (options) => {
            console.log('📤 Follow-up da interação:', JSON.stringify(options, null, 2));
            return { id: 'mock_followup_id' };
        },
        editReply: async (options) => {
            console.log('📤 Edit reply da interação:', JSON.stringify(options, null, 2));
            return { id: 'mock_edit_id' };
        },
        replied: false,
        deferred: false
    };
    
    try {
        // Testa o método diretamente
        console.log('🔄 Chamando CartButtonHandler.handleCancelPurchase...');
        await CartButtonHandler.handleCancelPurchase(mockInteraction);
        console.log('✅ Método executado com sucesso');
    } catch (error) {
        console.error('❌ Erro no método handleCancelPurchase:', error.message);
        console.error('Stack trace:', error.stack);
    }
    
    // Aguarda um pouco antes de finalizar
    setTimeout(() => {
        console.log('🏁 Teste finalizado');
        process.exit(0);
    }, 2000);
}

// Executa o teste
testCartCancelPurchase().catch(console.error);