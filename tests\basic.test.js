/**
 * Basic test to verify Jest is working
 */

describe('Basic Tests', () => {
    test('should pass basic test', () => {
        expect(1 + 1).toBe(2);
    });

    test('should handle strings', () => {
        expect('hello').toBe('hello');
        expect('hello world').toContain('world');
    });

    test('should handle arrays', () => {
        const arr = [1, 2, 3];
        expect(arr).toHaveLength(3);
        expect(arr).toContain(2);
    });

    test('should handle objects', () => {
        const obj = { name: 'test', value: 42 };
        expect(obj).toHaveProperty('name');
        expect(obj.name).toBe('test');
        expect(obj.value).toBe(42);
    });

    test('should handle async operations', async () => {
        const promise = Promise.resolve('success');
        await expect(promise).resolves.toBe('success');
    });

    test('should handle mock functions', () => {
        const mockFn = jest.fn();
        mockFn('test');
        
        expect(mockFn).toHaveBeenCalled();
        expect(mockFn).toHaveBeenCalledWith('test');
        expect(mockFn).toHaveBeenCalledTimes(1);
    });

    test('should handle error cases', () => {
        const errorFn = () => {
            throw new Error('Test error');
        };
        
        expect(errorFn).toThrow('Test error');
    });

    test('should handle boolean values', () => {
        expect(true).toBe(true);
        expect(false).toBe(false);
        expect(true).toBeTruthy();
        expect(false).toBeFalsy();
    });

    test('should handle null and undefined', () => {
        expect(null).toBeNull();
        expect(undefined).toBeUndefined();
        expect('defined').toBeDefined();
    });

    test('should handle numbers', () => {
        expect(2 + 2).toBe(4);
        expect(0.1 + 0.2).toBeCloseTo(0.3);
        expect(10).toBeGreaterThan(5);
        expect(5).toBeLessThan(10);
    });
});
