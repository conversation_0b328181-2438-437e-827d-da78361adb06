import dotenv from 'dotenv';
import mongoose from 'mongoose';

// Carrega variáveis de ambiente
dotenv.config();

async function testSimpleProduct() {
    try {
        console.log('🧪 Teste simples de criação de produto...');
        
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB');
        
        const db = mongoose.connection.db;
        const productsCollection = db.collection('products');
        const storesCollection = db.collection('stores');
        
        // Busca uma loja existente
        const store = await storesCollection.findOne();
        if (!store) {
            console.log('❌ Nenhuma loja encontrada');
            return;
        }
        
        console.log(`📍 Usando loja: ${store.name}`);
        
        // Tenta inserir um produto diretamente na coleção
        const testProduct = {
            name: 'Produto Teste Direto - ' + Date.now(),
            description: 'Teste direto na coleção',
            price: 19.99,
            stock: 0,
            category: 'digital',
            status: 'out_of_stock',
            createdBy: 'test-user',
            storeId: store._id,
            emoji: '🧪',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        console.log('📦 Inserindo produto...');
        const result = await productsCollection.insertOne(testProduct);
        console.log('✅ Produto inserido com sucesso!');
        console.log(`   ID: ${result.insertedId}`);
        
        // Tenta inserir outro produto
        const testProduct2 = {
            name: 'Produto Teste Direto 2 - ' + Date.now(),
            description: 'Segundo teste direto na coleção',
            price: 29.99,
            stock: 0,
            category: 'digital',
            status: 'out_of_stock',
            createdBy: 'test-user-2',
            storeId: store._id,
            emoji: '🎮',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        console.log('📦 Inserindo segundo produto...');
        const result2 = await productsCollection.insertOne(testProduct2);
        console.log('✅ Segundo produto inserido com sucesso!');
        console.log(`   ID: ${result2.insertedId}`);
        
        // Remove os produtos de teste
        console.log('🧹 Removendo produtos de teste...');
        await productsCollection.deleteMany({
            name: { $regex: /^Produto Teste Direto/ }
        });
        console.log('✅ Produtos de teste removidos');
        
        await mongoose.disconnect();
        console.log('✅ Teste concluído com sucesso!');
        console.log('🎉 Criação de produtos funcionando normalmente!');
        
    } catch (error) {
        console.error('❌ Erro durante o teste:', error);
        
        if (error.code === 11000) {
            console.error('💥 ERRO E11000 ainda presente!');
            console.error('   Detalhes:', error.message);
        }
        
        process.exit(1);
    }
}

testSimpleProduct();
