import { EmbedBuilder } from 'discord.js';
import { logger, LOG_TYPES } from './logger.js';
import BotConfig from '../models/BotConfig.js';
import { COLORS, EMOJIS } from '../config/constants.js';

/**
 * Sistema de logs do bot para canais específicos Discord
 * Integrado com o sistema de logging estruturado
 */
export class BotLogger {
    constructor() {
        this.client = null;
        this.configCache = new Map(); // Cache de configurações por guild
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutos
    }

    /**
     * Define o cliente Discord
     * @param {Client} client - Cliente do Discord
     */
    setClient(client) {
        this.client = client;
    }

    /**
     * Obtém configuração do bot com cache
     * @param {string} guildId - ID do servidor
     */
    async getBotConfig(guildId) {
        const cacheKey = guildId;
        const cached = this.configCache.get(cacheKey);

        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.config;
        }

        try {
            const config = await BotConfig.findByGuild(guildId);
            this.configCache.set(cacheKey, {
                config,
                timestamp: Date.now()
            });
            return config;
        } catch (error) {
            logger.error('Erro ao buscar configuração do bot:', error);
            return null;
        }
    }

    /**
     * Limpa cache de configuração
     * @param {string} guildId - ID do servidor (opcional, limpa tudo se não especificado)
     */
    clearConfigCache(guildId = null) {
        if (guildId) {
            this.configCache.delete(guildId);
        } else {
            this.configCache.clear();
        }
    }

    /**
     * Log estruturado para Discord
     * @param {string} level - Nível do log
     * @param {string} type - Tipo do log
     * @param {string} message - Mensagem
     * @param {Object} context - Contexto
     * @param {Object} data - Dados adicionais
     */
    async logToDiscord(level, type, message, context = {}, data = {}) {
        if (!this.client || !context.guildId) {
            return;
        }

        try {
            const config = await this.getBotConfig(context.guildId);
            if (!config) return;

            // Verifica se o tipo de log está habilitado
            const typeEnabled = config.logSettings?.enabledTypes?.[type.toLowerCase()];
            if (typeEnabled === false) return;

            // Verifica nível mínimo para Discord
            const minLevel = config.logSettings?.discordLogLevel || 'WARN';
            const levelPriority = { ERROR: 0, WARN: 1, INFO: 2, DEBUG: 3 };
            if (levelPriority[level] > levelPriority[minLevel]) return;

            // Determina o canal apropriado
            const channelId = this.getChannelForLogType(config, type, level);
            if (!channelId) return;

            const channel = this.client.channels.cache.get(channelId);
            if (!channel) {
                logger.warn(`Canal de log não encontrado: ${channelId}`);
                return;
            }

            const embed = this.createStructuredLogEmbed(level, type, message, context, data, config);
            await channel.send({ embeds: [embed] });

        } catch (error) {
            // Evita loop infinito de logs
            console.error('Erro ao enviar log estruturado para Discord:', error);
        }
    }

    /**
     * Determina o canal apropriado para o tipo de log
     * @param {Object} config - Configuração do bot
     * @param {string} type - Tipo do log
     * @param {string} level - Nível do log
     */
    getChannelForLogType(config, type, level) {
        // Prioridade para logs de erro
        if (level === 'ERROR' && config.logChannels?.error) {
            return config.logChannels.error;
        }

        // Mapeamento específico por tipo
        const typeChannelMap = {
            [LOG_TYPES.SYSTEM]: config.logChannels?.system,
            [LOG_TYPES.COMMAND]: config.logChannels?.commands,
            [LOG_TYPES.EVENT]: config.logChannels?.events,
            [LOG_TYPES.DATABASE]: config.logChannels?.database,
            [LOG_TYPES.API]: config.logChannels?.api,
            [LOG_TYPES.SECURITY]: config.logChannels?.moderation,
            [LOG_TYPES.ERROR]: config.logChannels?.error,
            [LOG_TYPES.USER_ACTION]: config.logChannels?.admin,
            [LOG_TYPES.PERFORMANCE]: config.logChannels?.debug
        };

        const specificChannel = typeChannelMap[type];
        if (specificChannel) return specificChannel;

        // Fallback para canais gerais
        if (level === 'ERROR' || type === LOG_TYPES.SECURITY) {
            return config.logChannels?.admin || config.adminLogChannelId;
        }

        return config.logChannels?.admin || config.adminLogChannelId;
    }

    /**
     * Cria embed estruturado para logs
     * @param {string} level - Nível do log
     * @param {string} type - Tipo do log
     * @param {string} message - Mensagem
     * @param {Object} context - Contexto
     * @param {Object} data - Dados adicionais
     * @param {Object} config - Configuração do bot
     */
    createStructuredLogEmbed(level, type, message, context, data, config) {
        const embed = new EmbedBuilder()
            .setColor(this.getColorByLevel(level))
            .setTitle(`${this.getEmojiByType(type)} ${type} - ${level}`)
            .setDescription(message)
            .setTimestamp()
            .setFooter({ text: `Log ${type}` });

        // Adiciona contexto
        if (context.userId) {
            embed.addFields({
                name: '👤 Usuário',
                value: `<@${context.userId}>`,
                inline: true
            });
        }

        if (context.command) {
            embed.addFields({
                name: '⚡ Comando',
                value: `\`/${context.command}\``,
                inline: true
            });
        }

        if (context.channel) {
            embed.addFields({
                name: '📍 Canal',
                value: `<#${context.channel}>`,
                inline: true
            });
        }

        // Adiciona dados específicos
        if (data && Object.keys(data).length > 0) {
            const dataStr = typeof data === 'string' ? data :
                Object.entries(data)
                    .map(([key, value]) => `**${key}:** ${value}`)
                    .join('\n');

            if (dataStr.length <= 1024) {
                embed.addFields({
                    name: '📊 Dados',
                    value: dataStr,
                    inline: false
                });
            }
        }

        // Adiciona stack trace para erros se habilitado
        if (level === 'ERROR' && data.stack && config.logSettings?.includeStackTrace) {
            const stackTrace = data.stack.substring(0, 1000); // Limita tamanho
            embed.addFields({
                name: '🔍 Stack Trace',
                value: `\`\`\`\n${stackTrace}\n\`\`\``,
                inline: false
            });
        }

        return embed;
    }
    /**
     * Envia log para canal de administradores
     * @param {Client} client - Cliente do Discord
     * @param {string} guildId - ID do servidor
     * @param {Object} logData - Dados do log
     */
    static async logAdmin(client, guildId, logData) {
        try {
            const config = await BotConfig.findByGuild(guildId);
            if (!config || !config.adminLogChannelId) {
                return;
            }

            const channel = client.channels.cache.get(config.adminLogChannelId);
            if (!channel) {
                logger.warn(`Canal de log admin não encontrado: ${config.adminLogChannelId}`);
                return;
            }

            const embed = this.createAdminLogEmbed(logData);
            await channel.send({ embeds: [embed] });

        } catch (error) {
            logger.error('Erro ao enviar log admin:', error);
        }
    }

    /**
     * Envia log para canal público
     * @param {Client} client - Cliente do Discord
     * @param {string} guildId - ID do servidor
     * @param {Object} logData - Dados do log
     */
    static async logPublic(client, guildId, logData) {
        try {
            const config = await BotConfig.findByGuild(guildId);
            if (!config || !config.publicLogChannelId) {
                return;
            }

            const channel = client.channels.cache.get(config.publicLogChannelId);
            if (!channel) {
                logger.warn(`Canal de log público não encontrado: ${config.publicLogChannelId}`);
                return;
            }

            const embed = this.createPublicLogEmbed(logData);
            await channel.send({ embeds: [embed] });

        } catch (error) {
            logger.error('Erro ao enviar log público:', error);
        }
    }

    /**
     * Cria embed para logs administrativos
     * @param {Object} logData - Dados do log
     */
    static createAdminLogEmbed(logData) {
        const { action, user, details, timestamp = new Date() } = logData;
        
        const embed = new EmbedBuilder()
            .setColor(this.getColorByAction(action))
            .setTitle(`${this.getEmojiByAction(action)} ${action}`)
            .setTimestamp(timestamp)
            .setFooter({ text: 'Log Administrativo' });

        if (user) {
            embed.addFields({
                name: '👤 Usuário',
                value: `<@${user.id}> (${user.tag})`,
                inline: true
            });
        }

        if (details) {
            if (typeof details === 'string') {
                embed.setDescription(details);
            } else {
                Object.entries(details).forEach(([key, value]) => {
                    embed.addFields({
                        name: key,
                        value: String(value),
                        inline: true
                    });
                });
            }
        }

        return embed;
    }

    /**
     * Cria embed para logs públicos
     * @param {Object} logData - Dados do log
     */
    static createPublicLogEmbed(logData) {
        const { action, user, details, timestamp = new Date() } = logData;
        
        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${EMOJIS.SUCCESS} ${action}`)
            .setTimestamp(timestamp);

        if (user && action.includes('Venda')) {
            embed.addFields({
                name: '🛒 Cliente',
                value: user.username || 'Cliente',
                inline: true
            });
        }

        if (details) {
            if (details.product) {
                embed.addFields({
                    name: '📦 Produto',
                    value: details.product,
                    inline: true
                });
            }
            if (details.amount) {
                embed.addFields({
                    name: '💰 Valor',
                    value: `R$ ${details.amount.toFixed(2)}`,
                    inline: true
                });
            }
            if (details.quantity) {
                embed.addFields({
                    name: '📊 Quantidade',
                    value: String(details.quantity),
                    inline: true
                });
            }
        }

        return embed;
    }

    /**
     * Retorna cor baseada no nível do log
     * @param {string} level - Nível do log
     */
    getColorByLevel(level) {
        switch (level) {
            case 'ERROR': return COLORS.ERROR;
            case 'WARN': return COLORS.WARNING;
            case 'INFO': return COLORS.INFO;
            case 'DEBUG': return COLORS.SECONDARY;
            default: return COLORS.INFO;
        }
    }

    /**
     * Retorna emoji baseado no tipo de log
     * @param {string} type - Tipo do log
     */
    getEmojiByType(type) {
        const emojiMap = {
            [LOG_TYPES.SYSTEM]: '⚙️',
            [LOG_TYPES.COMMAND]: '⚡',
            [LOG_TYPES.EVENT]: '📡',
            [LOG_TYPES.DATABASE]: '🗄️',
            [LOG_TYPES.API]: '🌐',
            [LOG_TYPES.SECURITY]: '🛡️',
            [LOG_TYPES.ERROR]: '❌',
            [LOG_TYPES.USER_ACTION]: '👤',
            [LOG_TYPES.PERFORMANCE]: '📊'
        };

        return emojiMap[type] || '📝';
    }

    /**
     * Retorna cor baseada na ação (compatibilidade)
     * @param {string} action - Ação realizada
     */
    static getColorByAction(action) {
        const actionLower = action.toLowerCase();

        if (actionLower.includes('erro') || actionLower.includes('falha')) {
            return COLORS.ERROR;
        }
        if (actionLower.includes('aviso') || actionLower.includes('warning')) {
            return COLORS.WARNING;
        }
        if (actionLower.includes('sucesso') || actionLower.includes('criado') || actionLower.includes('venda')) {
            return COLORS.SUCCESS;
        }
        if (actionLower.includes('deletado') || actionLower.includes('removido')) {
            return COLORS.ERROR;
        }
        if (actionLower.includes('editado') || actionLower.includes('modificado')) {
            return COLORS.WARNING;
        }

        return COLORS.INFO;
    }

    /**
     * Retorna emoji baseado na ação (compatibilidade)
     * @param {string} action - Ação realizada
     */
    static getEmojiByAction(action) {
        const actionLower = action.toLowerCase();

        if (actionLower.includes('erro') || actionLower.includes('falha')) {
            return EMOJIS.ERROR;
        }
        if (actionLower.includes('aviso') || actionLower.includes('warning')) {
            return EMOJIS.WARNING;
        }
        if (actionLower.includes('sucesso') || actionLower.includes('criado') || actionLower.includes('venda')) {
            return EMOJIS.SUCCESS;
        }
        if (actionLower.includes('deletado') || actionLower.includes('removido')) {
            return '🗑️';
        }
        if (actionLower.includes('editado') || actionLower.includes('modificado')) {
            return '✏️';
        }
        if (actionLower.includes('loja')) {
            return '🏪';
        }
        if (actionLower.includes('produto')) {
            return '📦';
        }

        return EMOJIS.INFO;
    }

    /**
     * Logs específicos para diferentes ações
     */
    static async logStoreCreated(client, guildId, user, storeName, channelId) {
        await this.logAdmin(client, guildId, {
            action: 'Loja Criada',
            user,
            details: {
                '🏪 Nome da Loja': storeName,
                '📍 Canal': `<#${channelId}>`,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    static async logStoreDeleted(client, guildId, user, storeName) {
        await this.logAdmin(client, guildId, {
            action: 'Loja Deletada',
            user,
            details: {
                '🏪 Nome da Loja': storeName,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    static async logProductCreated(client, guildId, user, productName, storeName) {
        await this.logAdmin(client, guildId, {
            action: 'Produto Criado',
            user,
            details: {
                '📦 Produto': productName,
                '🏪 Loja': storeName,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    static async logSaleCompleted(client, guildId, user, productName, amount, quantity) {
        // Log público
        await this.logPublic(client, guildId, {
            action: 'Venda Realizada',
            user,
            details: {
                product: productName,
                amount,
                quantity
            }
        });

        // Log admin
        await this.logAdmin(client, guildId, {
            action: 'Venda Concluída',
            user,
            details: {
                '📦 Produto': productName,
                '💰 Valor': `R$ ${amount.toFixed(2)}`,
                '📊 Quantidade': quantity,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    static async logConfigChanged(client, guildId, user, configType, newValue) {
        await this.logAdmin(client, guildId, {
            action: 'Configuração Alterada',
            user,
            details: {
                '⚙️ Tipo': configType,
                '🔄 Novo Valor': newValue,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    static async logRateLimitHit(client, guildId, user, command) {
        await this.logAdmin(client, guildId, {
            action: 'Rate Limit Atingido',
            user,
            details: {
                '⚡ Comando': command,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    // Métodos de conveniência para logs estruturados
    async logCommand(guildId, userId, command, success = true, details = {}) {
        const level = success ? 'INFO' : 'WARN';
        const message = success ?
            `Comando /${command} executado com sucesso` :
            `Falha na execução do comando /${command}`;

        await this.logToDiscord(level, LOG_TYPES.COMMAND, message, {
            guildId,
            userId,
            command
        }, details);
    }

    async logError(guildId, error, context = {}) {
        await this.logToDiscord('ERROR', LOG_TYPES.ERROR, error.message || 'Erro desconhecido', {
            guildId,
            ...context
        }, {
            stack: error.stack,
            name: error.name
        });
    }

    async logUserAction(guildId, userId, action, details = {}) {
        await this.logToDiscord('INFO', LOG_TYPES.USER_ACTION, action, {
            guildId,
            userId
        }, details);
    }

    async logSystemEvent(guildId, event, details = {}) {
        await this.logToDiscord('INFO', LOG_TYPES.SYSTEM, event, {
            guildId
        }, details);
    }

    async logDatabaseOperation(guildId, operation, collection, details = {}) {
        await this.logToDiscord('DEBUG', LOG_TYPES.DATABASE,
            `Operação ${operation} na coleção ${collection}`, {
            guildId
        }, details);
    }

    async logApiCall(guildId, api, endpoint, success = true, details = {}) {
        const level = success ? 'INFO' : 'WARN';
        const message = `Chamada API ${api} - ${endpoint} ${success ? 'sucesso' : 'falha'}`;

        await this.logToDiscord(level, LOG_TYPES.API, message, {
            guildId
        }, details);
    }

    async logSecurityEvent(guildId, event, userId = null, details = {}) {
        await this.logToDiscord('WARN', LOG_TYPES.SECURITY, event, {
            guildId,
            userId
        }, details);
    }

    async logPerformance(guildId, operation, duration, details = {}) {
        await this.logToDiscord('DEBUG', LOG_TYPES.PERFORMANCE,
            `${operation} executado em ${duration}ms`, {
            guildId
        }, details);
    }
}

// Instância global do BotLogger
export const botLogger = new BotLogger();