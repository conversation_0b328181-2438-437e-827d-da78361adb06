import { Events } from 'discord.js';
import { logger } from '../utils/logger.js';

export default {
    name: Events.GuildMemberAdd,
    async execute(member) {
        try {
            await logger.event(`Novo membro entrou no servidor`, {
                guildId: member.guild.id,
                userId: member.user.id
            }, {
                user: member.user.tag,
                guild: member.guild.name,
                memberCount: member.guild.memberCount,
                accountCreated: member.user.createdAt.toISOString(),
                isBot: member.user.bot
            });

            // Log de segurança para contas muito novas (possível spam/raid)
            const accountAge = Date.now() - member.user.createdAt.getTime();
            const dayInMs = 24 * 60 * 60 * 1000;
            
            if (accountAge < dayInMs) {
                await logger.security(`Conta muito nova entrou no servidor`, {
                    guildId: member.guild.id,
                    userId: member.user.id
                }, {
                    user: member.user.tag,
                    accountAge: `${Math.round(accountAge / (60 * 60 * 1000))} horas`,
                    guild: member.guild.name
                });
            }

        } catch (error) {
            await logger.logStructured('ERROR', 'EVENT', 'Erro no evento guildMemberAdd', {
                guildId: member.guild?.id,
                userId: member.user?.id
            }, {
                error: error.message,
                stack: error.stack
            });
        }
    }
};
