/**
 * Tests for Order model
 */

describe('Order Model', () => {
    let Order;
    let mockOrderInstance;

    beforeAll(async () => {
        // Mock order instance
        mockOrderInstance = {
            orderNumber: 'ORD-2024-001',
            userId: '*********',
            storeId: '507f1f77bcf86cd799439011',
            items: [
                {
                    productId: '507f1f77bcf86cd799439012',
                    name: 'Test Product',
                    price: 29.99,
                    quantity: 2,
                    subtotal: 59.98
                }
            ],
            pricing: {
                subtotal: 59.98,
                shipping: 15.00,
                tax: 5.25,
                discount: 0,
                total: 80.23
            },
            status: 'pending',
            paymentStatus: 'pending',
            paymentMethod: 'mercadopago',
            paymentId: 'mp_12345',
            shippingAddress: {
                street: 'Test Street',
                number: '123',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345-678',
                country: 'Brasil'
            },
            tracking: {
                code: null,
                carrier: null,
                status: 'preparing',
                estimatedDelivery: null,
                updates: []
            },
            notes: 'Test order notes',
            createdAt: new Date(),
            updatedAt: new Date(),
            save: jest.fn().mockResolvedValue(true),
            updateStatus: jest.fn(),
            updatePaymentStatus: jest.fn(),
            addTrackingInfo: jest.fn(),
            calculateTotal: jest.fn(),
            cancel: jest.fn(),
            refund: jest.fn()
        };

        // Mock Order model
        Order = {
            findById: jest.fn(),
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            findByUser: jest.fn(),
            findByStore: jest.fn(),
            findByStatus: jest.fn(),
            generateOrderNumber: jest.fn(),
            getOrderStats: jest.fn()
        };
    });

    describe('Schema Validation', () => {
        test('should require userId', () => {
            const orderData = { storeId: '123', items: [], pricing: {} };
            expect(orderData.userId).toBeUndefined();
        });

        test('should require storeId', () => {
            const orderData = { userId: '123', items: [], pricing: {} };
            expect(orderData.storeId).toBeUndefined();
        });

        test('should require items array', () => {
            const orderData = { userId: '123', storeId: '456', pricing: {} };
            expect(orderData.items).toBeUndefined();
        });

        test('should require pricing object', () => {
            const orderData = { userId: '123', storeId: '456', items: [] };
            expect(orderData.pricing).toBeUndefined();
        });

        test('should have default values', () => {
            expect(mockOrderInstance.status).toBe('pending');
            expect(mockOrderInstance.paymentStatus).toBe('pending');
            expect(mockOrderInstance.tracking.status).toBe('preparing');
        });

        test('should validate status enum', () => {
            const validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
            validStatuses.forEach(status => {
                expect(validStatuses).toContain(status);
            });
        });

        test('should validate paymentStatus enum', () => {
            const validPaymentStatuses = ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'];
            validPaymentStatuses.forEach(status => {
                expect(validPaymentStatuses).toContain(status);
            });
        });

        test('should validate paymentMethod enum', () => {
            const validPaymentMethods = ['mercadopago', 'pix', 'credit_card', 'debit_card', 'bank_transfer'];
            validPaymentMethods.forEach(method => {
                expect(validPaymentMethods).toContain(method);
            });
        });

        test('should validate items structure', () => {
            const validItem = mockOrderInstance.items[0];
            
            expect(validItem).toHaveProperty('productId');
            expect(validItem).toHaveProperty('name');
            expect(validItem).toHaveProperty('price');
            expect(validItem).toHaveProperty('quantity');
            expect(validItem).toHaveProperty('subtotal');
            
            expect(validItem.price).toBeGreaterThan(0);
            expect(validItem.quantity).toBeGreaterThan(0);
            expect(validItem.subtotal).toBe(validItem.price * validItem.quantity);
        });

        test('should validate pricing structure', () => {
            const pricing = mockOrderInstance.pricing;
            
            expect(pricing).toHaveProperty('subtotal');
            expect(pricing).toHaveProperty('shipping');
            expect(pricing).toHaveProperty('tax');
            expect(pricing).toHaveProperty('discount');
            expect(pricing).toHaveProperty('total');
            
            expect(pricing.subtotal).toBeGreaterThanOrEqual(0);
            expect(pricing.shipping).toBeGreaterThanOrEqual(0);
            expect(pricing.tax).toBeGreaterThanOrEqual(0);
            expect(pricing.discount).toBeGreaterThanOrEqual(0);
            expect(pricing.total).toBeGreaterThan(0);
        });
    });

    describe('Instance Methods', () => {
        test('updateStatus should update order status', async () => {
            const newStatus = 'confirmed';
            
            mockOrderInstance.updateStatus = jest.fn().mockImplementation(function(status) {
                this.status = status;
                this.updatedAt = new Date();
                return this.save();
            });

            await mockOrderInstance.updateStatus(newStatus);
            
            expect(mockOrderInstance.updateStatus).toHaveBeenCalledWith(newStatus);
            expect(mockOrderInstance.status).toBe(newStatus);
        });

        test('updatePaymentStatus should update payment status', async () => {
            const newPaymentStatus = 'completed';
            
            mockOrderInstance.updatePaymentStatus = jest.fn().mockImplementation(function(status) {
                this.paymentStatus = status;
                if (status === 'completed') {
                    this.status = 'confirmed';
                }
                this.updatedAt = new Date();
                return this.save();
            });

            await mockOrderInstance.updatePaymentStatus(newPaymentStatus);
            
            expect(mockOrderInstance.updatePaymentStatus).toHaveBeenCalledWith(newPaymentStatus);
            expect(mockOrderInstance.paymentStatus).toBe(newPaymentStatus);
            expect(mockOrderInstance.status).toBe('confirmed');
        });

        test('addTrackingInfo should add tracking information', async () => {
            const trackingInfo = {
                code: 'BR*********',
                carrier: 'Correios',
                estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
            };
            
            mockOrderInstance.addTrackingInfo = jest.fn().mockImplementation(function(info) {
                Object.assign(this.tracking, info);
                this.tracking.status = 'shipped';
                this.status = 'shipped';
                this.updatedAt = new Date();
                return this.save();
            });

            await mockOrderInstance.addTrackingInfo(trackingInfo);
            
            expect(mockOrderInstance.addTrackingInfo).toHaveBeenCalledWith(trackingInfo);
            expect(mockOrderInstance.tracking.code).toBe(trackingInfo.code);
            expect(mockOrderInstance.tracking.carrier).toBe(trackingInfo.carrier);
            expect(mockOrderInstance.tracking.status).toBe('shipped');
            expect(mockOrderInstance.status).toBe('shipped');
        });

        test('calculateTotal should recalculate order total', async () => {
            mockOrderInstance.calculateTotal = jest.fn().mockImplementation(function() {
                const subtotal = this.items.reduce((sum, item) => sum + item.subtotal, 0);
                this.pricing.subtotal = subtotal;
                this.pricing.total = subtotal + this.pricing.shipping + this.pricing.tax - this.pricing.discount;
                return this.save();
            });

            await mockOrderInstance.calculateTotal();
            
            expect(mockOrderInstance.calculateTotal).toHaveBeenCalled();
            expect(mockOrderInstance.pricing.subtotal).toBe(59.98);
            expect(mockOrderInstance.pricing.total).toBeCloseTo(80.23, 2);
        });

        test('cancel should cancel the order', async () => {
            mockOrderInstance.cancel = jest.fn().mockImplementation(function(reason) {
                if (this.status === 'delivered') {
                    throw new Error('Não é possível cancelar pedido já entregue');
                }
                this.status = 'cancelled';
                this.paymentStatus = 'cancelled';
                this.notes = (this.notes || '') + `\nCancelado: ${reason}`;
                this.updatedAt = new Date();
                return this.save();
            });

            await mockOrderInstance.cancel('Solicitação do cliente');
            
            expect(mockOrderInstance.cancel).toHaveBeenCalledWith('Solicitação do cliente');
            expect(mockOrderInstance.status).toBe('cancelled');
            expect(mockOrderInstance.paymentStatus).toBe('cancelled');
            expect(mockOrderInstance.notes).toContain('Cancelado: Solicitação do cliente');
        });

        test('cancel should throw error for delivered orders', async () => {
            mockOrderInstance.status = 'delivered';
            
            mockOrderInstance.cancel = jest.fn().mockImplementation(function(reason) {
                if (this.status === 'delivered') {
                    throw new Error('Não é possível cancelar pedido já entregue');
                }
                this.status = 'cancelled';
                this.paymentStatus = 'cancelled';
                return this.save();
            });

            try {
                await mockOrderInstance.cancel('Test reason');
                fail('Should have thrown an error');
            } catch (error) {
                expect(error.message).toBe('Não é possível cancelar pedido já entregue');
            }
        });

        test('refund should process refund', async () => {
            mockOrderInstance.refund = jest.fn().mockImplementation(function(amount, reason) {
                if (this.paymentStatus !== 'completed') {
                    throw new Error('Só é possível reembolsar pedidos pagos');
                }
                this.status = 'refunded';
                this.paymentStatus = 'refunded';
                this.notes = (this.notes || '') + `\nReembolso: R$ ${amount} - ${reason}`;
                this.updatedAt = new Date();
                return this.save();
            });

            mockOrderInstance.paymentStatus = 'completed';
            await mockOrderInstance.refund(80.23, 'Produto defeituoso');
            
            expect(mockOrderInstance.refund).toHaveBeenCalledWith(80.23, 'Produto defeituoso');
            expect(mockOrderInstance.status).toBe('refunded');
            expect(mockOrderInstance.paymentStatus).toBe('refunded');
            expect(mockOrderInstance.notes).toContain('Reembolso: R$ 80.23 - Produto defeituoso');
        });
    });

    describe('Static Methods', () => {
        test('findByUser should find orders by user ID', async () => {
            const userId = '*********';
            const mockOrders = [mockOrderInstance];
            
            Order.findByUser = jest.fn().mockResolvedValue(mockOrders);
            
            const result = await Order.findByUser(userId);
            
            expect(Order.findByUser).toHaveBeenCalledWith(userId);
            expect(result).toEqual(mockOrders);
        });

        test('findByStore should find orders by store ID', async () => {
            const storeId = '507f1f77bcf86cd799439011';
            const mockOrders = [mockOrderInstance];
            
            Order.findByStore = jest.fn().mockResolvedValue(mockOrders);
            
            const result = await Order.findByStore(storeId);
            
            expect(Order.findByStore).toHaveBeenCalledWith(storeId);
            expect(result).toEqual(mockOrders);
        });

        test('findByStatus should find orders by status', async () => {
            const status = 'pending';
            const mockOrders = [mockOrderInstance];
            
            Order.findByStatus = jest.fn().mockResolvedValue(mockOrders);
            
            const result = await Order.findByStatus(status);
            
            expect(Order.findByStatus).toHaveBeenCalledWith(status);
            expect(result).toEqual(mockOrders);
        });

        test('generateOrderNumber should generate unique order number', async () => {
            const orderNumber = 'ORD-2024-002';
            
            Order.generateOrderNumber = jest.fn().mockResolvedValue(orderNumber);
            
            const result = await Order.generateOrderNumber();
            
            expect(Order.generateOrderNumber).toHaveBeenCalled();
            expect(result).toBe(orderNumber);
        });

        test('getOrderStats should return order statistics', async () => {
            const stats = {
                totalOrders: 100,
                pendingOrders: 25,
                completedOrders: 60,
                cancelledOrders: 15,
                totalRevenue: 15000.50
            };
            
            Order.getOrderStats = jest.fn().mockResolvedValue(stats);
            
            const result = await Order.getOrderStats();
            
            expect(Order.getOrderStats).toHaveBeenCalled();
            expect(result).toEqual(stats);
        });
    });

    describe('Edge Cases and Error Handling', () => {
        test('should handle empty items array', () => {
            const emptyOrder = { ...mockOrderInstance, items: [] };
            expect(emptyOrder.items).toHaveLength(0);
        });

        test('should handle invalid pricing calculations', () => {
            const invalidPricing = {
                subtotal: -10,
                shipping: -5,
                tax: -2,
                discount: 150,
                total: -50
            };
            
            expect(invalidPricing.subtotal).toBeLessThan(0);
            expect(invalidPricing.total).toBeLessThan(0);
        });

        test('should handle missing shipping address', () => {
            const orderWithoutAddress = { ...mockOrderInstance, shippingAddress: null };
            expect(orderWithoutAddress.shippingAddress).toBeNull();
        });

        test('should handle invalid status transitions', async () => {
            mockOrderInstance.updateStatus = jest.fn().mockImplementation(function(status) {
                const validTransitions = {
                    'pending': ['confirmed', 'cancelled'],
                    'confirmed': ['processing', 'cancelled'],
                    'processing': ['shipped', 'cancelled'],
                    'shipped': ['delivered'],
                    'delivered': [],
                    'cancelled': [],
                    'refunded': []
                };
                
                if (!validTransitions[this.status].includes(status)) {
                    throw new Error(`Transição inválida de ${this.status} para ${status}`);
                }
                
                this.status = status;
                return this.save();
            });

            mockOrderInstance.status = 'delivered';
            
            try {
                await mockOrderInstance.updateStatus('pending');
                fail('Should have thrown an error');
            } catch (error) {
                expect(error.message).toBe('Transição inválida de delivered para pending');
            }
        });

        test('should handle concurrent order updates', async () => {
            const order1 = { ...mockOrderInstance };
            const order2 = { ...mockOrderInstance };
            
            order1.updateStatus = jest.fn().mockImplementation(function(status) {
                this.status = status;
                return Promise.resolve();
            });
            
            order2.updateStatus = jest.fn().mockImplementation(function(status) {
                this.status = status;
                return Promise.resolve();
            });
            
            await Promise.all([
                order1.updateStatus('confirmed'),
                order2.updateStatus('cancelled')
            ]);
            
            expect(order1.status).toBe('confirmed');
            expect(order2.status).toBe('cancelled');
        });

        test('should handle malformed order numbers', () => {
            const invalidOrderNumbers = ['', null, undefined, 'INVALID', '123'];
            
            invalidOrderNumbers.forEach(orderNumber => {
                const isValid = typeof orderNumber === 'string' && 
                               orderNumber.match(/^ORD-\d{4}-\d{3,}$/);
                expect(isValid).toBeFalsy();
            });
        });

        test('should validate item quantities', () => {
            const invalidItems = [
                { productId: '123', name: 'Test', price: 10, quantity: 0, subtotal: 0 },
                { productId: '123', name: 'Test', price: 10, quantity: -1, subtotal: -10 },
                { productId: '123', name: 'Test', price: -5, quantity: 2, subtotal: -10 }
            ];
            
            invalidItems.forEach(item => {
                expect(item.quantity <= 0 || item.price <= 0 || item.subtotal <= 0).toBeTruthy();
            });
        });
    });
});
