import { Client, GatewayIntentBits, Collection } from 'discord.js';
import { config } from 'dotenv';
import { connectDatabase, setupDatabaseLogging } from './src/database/connection.js';
import { loadCommands } from './src/utils/commandLoader.js';
import { loadEvents } from './src/utils/eventLoader.js';
import { logger } from './src/utils/logger.js';
import { botLogger } from './src/utils/botLogger.js';

// Carrega variáveis de ambiente
config();

// Cria uma nova instância do cliente Discord
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
    ]
});

// Cria uma coleção para armazenar comandos
client.commands = new Collection();

// Função principal para inicializar o bot
async function initializeBot() {
    try {
        await logger.system('🚀 Iniciando o bot...', {}, {
            nodeVersion: process.version,
            environment: process.env.NODE_ENV || 'production'
        });

        // Configura logging de banco de dados
        setupDatabaseLogging();

        // Conecta ao banco de dados
        await connectDatabase();
        await logger.system('✅ Banco de dados conectado');

        // Configura integração entre sistemas de logging
        logger.setDiscordLogger(botLogger);
        botLogger.setClient(client);

        // Carrega comandos e eventos
        await loadCommands(client);
        await logger.system(`📋 ${client.commands.size} comandos carregados`);

        await loadEvents(client);
        await logger.system('📡 Eventos carregados');

        // Faz login no Discord
        await client.login(process.env.DISCORD_TOKEN);

    } catch (error) {
        await logger.logStructured('ERROR', 'SYSTEM', 'Erro ao inicializar o bot', {}, {
            error: error.message,
            stack: error.stack
        });
        process.exit(1);
    }
}

// Tratamento de erros não capturados
process.on('unhandledRejection', async (reason, promise) => {
    await logger.logStructured('ERROR', 'SYSTEM', 'Unhandled Promise Rejection', {}, {
        reason: reason?.message || reason,
        stack: reason?.stack,
        promise: promise.toString()
    });
});

process.on('uncaughtException', async (error) => {
    await logger.logStructured('ERROR', 'SYSTEM', 'Uncaught Exception', {}, {
        error: error.message,
        stack: error.stack
    });
    process.exit(1);
});

// Tratamento de sinais do sistema
process.on('SIGINT', async () => {
    await logger.system('🛑 Bot sendo encerrado (SIGINT)');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    await logger.system('🛑 Bot sendo encerrado (SIGTERM)');
    process.exit(0);
});

// Inicializa o bot
initializeBot();
