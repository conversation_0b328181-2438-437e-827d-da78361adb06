import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Testing imports...');

async function testImport(modulePath) {
  try {
    const moduleUrl = pathToFileURL(modulePath).href;
    await import(moduleUrl);
    console.log(`${path.basename(modulePath)} OK`);
  } catch (e) {
    console.error(`Error in ${path.basename(modulePath)}:`, e);
  }
}

(async () => {
  await testImport(path.join(__dirname, 'src', 'handlers', 'slashCommandHandler.js'));
  await testImport(path.join(__dirname, 'src', 'handlers', 'autocompleteHandler.js'));
  await testImport(path.join(__dirname, 'src', 'handlers', 'buttonHandler.js'));
  await testImport(path.join(__dirname, 'src', 'handlers', 'selectMenuHandler.js'));
  await testImport(path.join(__dirname, 'src', 'handlers', 'modalHandler.js'));
  await testImport(path.join(__dirname, 'src', 'utils', 'logger.js'));
  await testImport(path.join(__dirname, 'src', 'events', 'interactionCreate.js'));

  console.log('Test complete.');
})();