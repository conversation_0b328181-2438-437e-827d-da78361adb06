import mongoose from 'mongoose';

const orderItemSchema = new mongoose.Schema({
    productId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product',
        required: true
    },
    productName: {
        type: String,
        required: true
    },
    quantity: {
        type: Number,
        required: true,
        min: 1
    },
    unitPrice: {
        type: Number,
        required: true,
        min: 0
    },
    totalPrice: {
        type: Number,
        required: true,
        min: 0
    }
});

const orderSchema = new mongoose.Schema({
    // Identificação do pedido
    orderNumber: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    
    // Cliente
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    customerDiscordId: {
        type: String,
        required: true,
        index: true
    },
    
    // Itens do pedido
    items: [orderItemSchema],
    
    // Valores
    subtotal: {
        type: Number,
        required: true,
        min: 0
    },
    shippingCost: {
        type: Number,
        default: 0,
        min: 0
    },
    discount: {
        type: Number,
        default: 0,
        min: 0
    },
    total: {
        type: Number,
        required: true,
        min: 0
    },
    
    // Status do pedido
    status: {
        type: String,
        enum: [
            'pending',      // Aguardando pagamento
            'paid',         // Pago
            'processing',   // Processando
            'shipped',      // Enviado
            'delivered',    // Entregue
            'cancelled',    // Cancelado
            'refunded'      // Reembolsado
        ],
        default: 'pending'
    },
    
    // Informações de pagamento
    paymentMethod: {
        type: String,
        enum: ['pix', 'credit_card', 'debit_card', 'bank_transfer', 'balance'],
        required: true
    },
    paymentStatus: {
        type: String,
        enum: ['pending', 'approved', 'rejected', 'cancelled', 'refunded'],
        default: 'pending'
    },
    paymentId: String, // ID do pagamento no gateway
    
    // Endereço de entrega (para produtos físicos)
    shippingAddress: {
        street: String,
        number: String,
        complement: String,
        neighborhood: String,
        city: String,
        state: String,
        zipCode: String,
        country: String
    },
    
    // Rastreamento
    trackingCode: String,
    
    // Datas importantes
    paidAt: Date,
    shippedAt: Date,
    deliveredAt: Date,
    cancelledAt: Date,
    
    // Observações
    notes: String,
    adminNotes: String
}, {
    timestamps: true,
    collection: 'orders'
});

// Índices para otimização
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ customerDiscordId: 1, createdAt: -1 });
orderSchema.index({ status: 1 });
orderSchema.index({ paymentStatus: 1 });
orderSchema.index({ createdAt: -1 });

// Middleware para gerar número do pedido
orderSchema.pre('save', async function(next) {
    if (this.isNew && !this.orderNumber) {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        this.orderNumber = `ORD-${timestamp}-${random}`.toUpperCase();
    }
    next();
});

// Métodos do schema
orderSchema.methods.updateStatus = function(newStatus, adminId = null) {
    const oldStatus = this.status;
    this.status = newStatus;
    
    // Atualiza datas baseado no status
    const now = new Date();
    switch (newStatus) {
        case 'paid':
            this.paidAt = now;
            this.paymentStatus = 'approved';
            break;
        case 'shipped':
            this.shippedAt = now;
            break;
        case 'delivered':
            this.deliveredAt = now;
            break;
        case 'cancelled':
            this.cancelledAt = now;
            this.paymentStatus = 'cancelled';
            break;
    }
    
    if (adminId) {
        this.adminNotes = `Status alterado de ${oldStatus} para ${newStatus} por ${adminId} em ${now.toISOString()}`;
    }
    
    return this.save();
};

orderSchema.methods.calculateTotal = function() {
    this.subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
    this.total = this.subtotal + this.shippingCost - this.discount;
    return this.total;
};

orderSchema.methods.addItem = function(productId, productName, quantity, unitPrice) {
    const totalPrice = quantity * unitPrice;
    
    this.items.push({
        productId,
        productName,
        quantity,
        unitPrice,
        totalPrice
    });
    
    this.calculateTotal();
    return this.save();
};

// Métodos estáticos
orderSchema.statics.findByCustomer = function(discordId) {
    return this.find({ customerDiscordId: discordId }).sort({ createdAt: -1 });
};

orderSchema.statics.findByStatus = function(status) {
    return this.find({ status }).sort({ createdAt: -1 });
};

orderSchema.statics.findPendingPayments = function() {
    return this.find({ 
        status: 'pending',
        paymentStatus: 'pending'
    }).sort({ createdAt: 1 });
};

export default mongoose.model('Order', orderSchema);
