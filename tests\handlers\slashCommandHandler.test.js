/**
 * Tests for slash command handler
 */

describe('Slash Command Handler', () => {
    let slashCommandHandler;
    let mockInteraction;
    let mockClient;
    let mockRateLimiter;

    beforeAll(async () => {
        // Mock rate limiter
        mockRateLimiter = {
            isRateLimited: jest.fn().mockReturnValue(false),
            addRequest: jest.fn(),
            getRemainingTime: jest.fn().mockReturnValue(0)
        };

        // Mock client
        mockClient = {
            commands: new Map(),
            user: { tag: 'TestBot#1234' }
        };

        // Mock interaction
        mockInteraction = {
            commandName: 'ping',
            user: { 
                id: '123456789',
                tag: 'TestUser#1234'
            },
            guild: { 
                id: '987654321',
                name: 'Test Guild'
            },
            client: mockClient,
            reply: jest.fn().mockResolvedValue(true),
            followUp: jest.fn().mockResolvedValue(true),
            editReply: jest.fn().mockResolvedValue(true),
            deferReply: jest.fn().mockResolvedValue(true),
            replied: false,
            deferred: false
        };

        // Mock command
        const mockCommand = {
            data: { name: 'ping' },
            execute: jest.fn().mockResolvedValue(true)
        };

        mockClient.commands.set('ping', mockCommand);

        // Mock slash command handler
        slashCommandHandler = {
            async handle(interaction) {
                try {
                    // Rate limiting check
                    const userId = interaction.user.id;
                    const guildId = interaction.guild?.id || 'dm';
                    const rateLimitKey = `${userId}:${guildId}:${interaction.commandName}`;
                    
                    if (mockRateLimiter.isRateLimited(rateLimitKey)) {
                        const remainingTime = mockRateLimiter.getRemainingTime(rateLimitKey);
                        const embed = {
                            color: 0xFF0000,
                            title: '⏰ Rate Limit',
                            description: `Você está usando comandos muito rapidamente. Tente novamente em ${remainingTime}s.`,
                            timestamp: new Date().toISOString()
                        };
                        
                        await interaction.reply({ embeds: [embed], ephemeral: true });
                        return;
                    }

                    // Get command
                    const command = interaction.client.commands.get(interaction.commandName);
                    
                    if (!command) {
                        const embed = {
                            color: 0xFF0000,
                            title: '❌ Comando não encontrado',
                            description: `O comando \`${interaction.commandName}\` não foi encontrado.`,
                            timestamp: new Date().toISOString()
                        };
                        
                        await interaction.reply({ embeds: [embed], ephemeral: true });
                        return;
                    }

                    // Add to rate limiter
                    mockRateLimiter.addRequest(rateLimitKey);

                    // Execute command
                    await command.execute(interaction);
                    
                } catch (error) {
                    console.error('Error executing slash command:', error);
                    
                    const errorEmbed = {
                        color: 0xFF0000,
                        title: '❌ Erro',
                        description: 'Ocorreu um erro ao executar este comando.',
                        timestamp: new Date().toISOString()
                    };

                    if (interaction.replied || interaction.deferred) {
                        await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
                    } else {
                        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                    }
                }
            }
        };
    });

    describe('Command Execution', () => {
        beforeEach(() => {
            jest.clearAllMocks();
            mockRateLimiter.isRateLimited.mockReturnValue(false);
            mockInteraction.replied = false;
            mockInteraction.deferred = false;
            mockInteraction.reply = jest.fn().mockResolvedValue(true);
            mockInteraction.followUp = jest.fn().mockResolvedValue(true);
            mockClient.commands = new Map();
            mockClient.commands.set('ping', {
                data: { name: 'ping' },
                execute: jest.fn().mockResolvedValue(true)
            });
        });

        test('should execute valid command', async () => {
            await slashCommandHandler.handle(mockInteraction);

            const command = mockClient.commands.get('ping');
            expect(command.execute).toHaveBeenCalledWith(mockInteraction);
            expect(mockRateLimiter.addRequest).toHaveBeenCalled();
        });

        test('should handle non-existent command', async () => {
            mockInteraction.commandName = 'nonexistent';
            
            await slashCommandHandler.handle(mockInteraction);

            expect(mockInteraction.reply).toHaveBeenCalledWith({
                embeds: [expect.objectContaining({
                    title: '❌ Comando não encontrado',
                    description: 'O comando `nonexistent` não foi encontrado.'
                })],
                ephemeral: true
            });
        });

        test('should handle rate limited user', async () => {
            mockRateLimiter.isRateLimited.mockReturnValue(true);
            mockRateLimiter.getRemainingTime.mockReturnValue(30);
            
            await slashCommandHandler.handle(mockInteraction);

            expect(mockInteraction.reply).toHaveBeenCalledWith({
                embeds: [expect.objectContaining({
                    title: '⏰ Rate Limit',
                    description: 'Você está usando comandos muito rapidamente. Tente novamente em 30s.'
                })],
                ephemeral: true
            });

            const command = mockClient.commands.get('ping');
            expect(command.execute).not.toHaveBeenCalled();
        });

        test('should add request to rate limiter for valid commands', async () => {
            // Ensure we're using ping command
            mockInteraction.commandName = 'ping';

            await slashCommandHandler.handle(mockInteraction);

            const expectedKey = `${mockInteraction.user.id}:${mockInteraction.guild.id}:ping`;
            expect(mockRateLimiter.addRequest).toHaveBeenCalledWith(expectedKey);
        });

        test('should handle DM commands with proper rate limit key', async () => {
            mockInteraction.guild = null;
            
            await slashCommandHandler.handle(mockInteraction);

            const expectedKey = `${mockInteraction.user.id}:dm:${mockInteraction.commandName}`;
            expect(mockRateLimiter.isRateLimited).toHaveBeenCalledWith(expectedKey);
        });
    });

    describe('Error Handling', () => {
        beforeEach(() => {
            jest.clearAllMocks();
            mockRateLimiter.isRateLimited.mockReturnValue(false);
            mockInteraction.replied = false;
            mockInteraction.deferred = false;
            mockInteraction.reply = jest.fn().mockResolvedValue(true);
            mockInteraction.followUp = jest.fn().mockResolvedValue(true);
            mockClient.commands = new Map();
        });

        test('should handle command execution error', async () => {
            // Ensure ping command exists and fails
            mockInteraction.commandName = 'ping';
            mockClient.commands.set('ping', {
                data: { name: 'ping' },
                execute: jest.fn().mockRejectedValue(new Error('Command failed'))
            });

            await slashCommandHandler.handle(mockInteraction);

            expect(mockInteraction.reply).toHaveBeenCalledWith({
                embeds: [expect.objectContaining({
                    title: '❌ Erro',
                    description: 'Ocorreu um erro ao executar este comando.'
                })],
                ephemeral: true
            });
        });

        test('should use followUp for already replied interactions', async () => {
            mockInteraction.replied = true;
            mockInteraction.commandName = 'ping';
            // Ensure ping command exists and fails
            mockClient.commands.set('ping', {
                data: { name: 'ping' },
                execute: jest.fn().mockRejectedValue(new Error('Command failed'))
            });

            await slashCommandHandler.handle(mockInteraction);

            expect(mockInteraction.followUp).toHaveBeenCalledWith({
                embeds: [expect.objectContaining({
                    title: '❌ Erro',
                    description: 'Ocorreu um erro ao executar este comando.'
                })],
                ephemeral: true
            });
        });

        test('should use followUp for deferred interactions', async () => {
            mockInteraction.deferred = true;
            mockInteraction.commandName = 'ping';
            // Ensure ping command exists and fails
            mockClient.commands.set('ping', {
                data: { name: 'ping' },
                execute: jest.fn().mockRejectedValue(new Error('Command failed'))
            });

            await slashCommandHandler.handle(mockInteraction);

            expect(mockInteraction.followUp).toHaveBeenCalledWith({
                embeds: [expect.objectContaining({
                    title: '❌ Erro',
                    description: 'Ocorreu um erro ao executar este comando.'
                })],
                ephemeral: true
            });
        });

        test('should handle rate limiter errors', async () => {
            mockRateLimiter.isRateLimited.mockImplementation(() => {
                throw new Error('Rate limiter error');
            });
            
            await slashCommandHandler.handle(mockInteraction);

            expect(mockInteraction.reply).toHaveBeenCalledWith({
                embeds: [expect.objectContaining({
                    title: '❌ Erro',
                    description: 'Ocorreu um erro ao executar este comando.'
                })],
                ephemeral: true
            });
        });

        test('should handle missing client commands', async () => {
            mockInteraction.client.commands = null;
            
            await slashCommandHandler.handle(mockInteraction);

            expect(mockInteraction.reply).toHaveBeenCalledWith({
                embeds: [expect.objectContaining({
                    title: '❌ Erro',
                    description: 'Ocorreu um erro ao executar este comando.'
                })],
                ephemeral: true
            });
        });

        test('should handle interaction reply failures', async () => {
            // Create a fresh mock for this test to avoid interference
            const testInteraction = {
                ...mockInteraction,
                reply: jest.fn().mockRejectedValue(new Error('Reply failed')),
                commandName: 'nonexistent'
            };

            try {
                await slashCommandHandler.handle(testInteraction);
            } catch (error) {
                // Expected to fail, but should be handled gracefully
                expect(error.message).toBe('Reply failed');
            }
        });
    });

    describe('Rate Limiting', () => {
        beforeEach(() => {
            jest.clearAllMocks();
            mockInteraction.replied = false;
            mockInteraction.deferred = false;
            mockInteraction.reply = jest.fn().mockResolvedValue(true);
            mockInteraction.followUp = jest.fn().mockResolvedValue(true);
            mockInteraction.guild = { id: '987654321' };
            mockClient.commands = new Map();
            mockClient.commands.set('ping', {
                data: { name: 'ping' },
                execute: jest.fn().mockResolvedValue(true)
            });
        });

        test('should check rate limit before command execution', async () => {
            await slashCommandHandler.handle(mockInteraction);

            const expectedKey = `${mockInteraction.user.id}:${mockInteraction.guild.id}:${mockInteraction.commandName}`;
            expect(mockRateLimiter.isRateLimited).toHaveBeenCalledWith(expectedKey);
        });

        test('should not execute command when rate limited', async () => {
            mockRateLimiter.isRateLimited.mockReturnValue(true);
            
            await slashCommandHandler.handle(mockInteraction);

            const command = mockClient.commands.get('ping');
            expect(command.execute).not.toHaveBeenCalled();
            expect(mockRateLimiter.addRequest).not.toHaveBeenCalled();
        });

        test('should show remaining time in rate limit message', async () => {
            mockRateLimiter.isRateLimited.mockReturnValue(true);
            mockRateLimiter.getRemainingTime.mockReturnValue(45);
            
            await slashCommandHandler.handle(mockInteraction);

            expect(mockInteraction.reply).toHaveBeenCalledWith({
                embeds: [expect.objectContaining({
                    description: 'Você está usando comandos muito rapidamente. Tente novamente em 45s.'
                })],
                ephemeral: true
            });
        });

        test('should handle different rate limit keys for different users', async () => {
            const user1 = { ...mockInteraction, user: { id: '111', tag: 'User1#1111' }, guild: { id: '987654321' }, commandName: 'ping' };
            const user2 = { ...mockInteraction, user: { id: '222', tag: 'User2#2222' }, guild: { id: '987654321' }, commandName: 'ping' };
            
            await slashCommandHandler.handle(user1);
            await slashCommandHandler.handle(user2);

            expect(mockRateLimiter.isRateLimited).toHaveBeenCalledWith(`111:987654321:ping`);
            expect(mockRateLimiter.isRateLimited).toHaveBeenCalledWith(`222:987654321:ping`);
        });

        test('should handle different rate limit keys for different guilds', async () => {
            const guild1 = { ...mockInteraction, guild: { id: '111', name: 'Guild1' }, commandName: 'ping' };
            const guild2 = { ...mockInteraction, guild: { id: '222', name: 'Guild2' }, commandName: 'ping' };
            
            await slashCommandHandler.handle(guild1);
            await slashCommandHandler.handle(guild2);

            expect(mockRateLimiter.isRateLimited).toHaveBeenCalledWith(`${mockInteraction.user.id}:111:ping`);
            expect(mockRateLimiter.isRateLimited).toHaveBeenCalledWith(`${mockInteraction.user.id}:222:ping`);
        });

        test('should handle different rate limit keys for different commands', async () => {
            const pingCmd = { ...mockInteraction, commandName: 'ping', guild: { id: '987654321' } };
            const helpCmd = { ...mockInteraction, commandName: 'help', guild: { id: '987654321' } };

            // Add help command to client
            mockClient.commands.set('help', {
                data: { name: 'help' },
                execute: jest.fn().mockResolvedValue(true)
            });

            await slashCommandHandler.handle(pingCmd);
            await slashCommandHandler.handle(helpCmd);

            expect(mockRateLimiter.isRateLimited).toHaveBeenCalledWith(`${mockInteraction.user.id}:987654321:ping`);
            expect(mockRateLimiter.isRateLimited).toHaveBeenCalledWith(`${mockInteraction.user.id}:987654321:help`);
        });
    });

    describe('Integration', () => {
        beforeEach(() => {
            jest.clearAllMocks();
            mockRateLimiter.isRateLimited.mockReturnValue(false);
            mockInteraction.replied = false;
            mockInteraction.deferred = false;
            mockInteraction.reply = jest.fn().mockResolvedValue(true);
            mockInteraction.followUp = jest.fn().mockResolvedValue(true);
            mockClient.commands = new Map();
        });

        test('should work with multiple commands', async () => {
            const commands = ['ping', 'help', 'info'];
            
            commands.forEach(cmdName => {
                mockClient.commands.set(cmdName, {
                    data: { name: cmdName },
                    execute: jest.fn().mockResolvedValue(true)
                });
            });

            for (const cmdName of commands) {
                mockInteraction.commandName = cmdName;
                await slashCommandHandler.handle(mockInteraction);
                
                const command = mockClient.commands.get(cmdName);
                expect(command.execute).toHaveBeenCalledWith(mockInteraction);
            }
        });

        test('should maintain proper error state across multiple calls', async () => {
            // Ensure ping command exists and interaction uses ping
            mockInteraction.commandName = 'ping';
            mockClient.commands.set('ping', {
                data: { name: 'ping' },
                execute: jest.fn().mockResolvedValue(true)
            });

            // First call succeeds
            await slashCommandHandler.handle(mockInteraction);

            // Second call fails
            const command = mockClient.commands.get('ping');
            command.execute.mockRejectedValueOnce(new Error('Command failed'));

            await slashCommandHandler.handle(mockInteraction);

            // Third call succeeds again
            command.execute.mockResolvedValueOnce(true);
            await slashCommandHandler.handle(mockInteraction);

            expect(command.execute).toHaveBeenCalledTimes(3);
        });

        test('should handle concurrent command executions', async () => {
            // Set up ping command
            mockClient.commands.set('ping', {
                data: { name: 'ping' },
                execute: jest.fn().mockResolvedValue(true)
            });

            const interactions = Array(5).fill().map((_, i) => ({
                ...mockInteraction,
                user: { id: `user${i}`, tag: `User${i}#1234` },
                guild: { id: '987654321' },
                commandName: 'ping',
                reply: jest.fn().mockResolvedValue(true)
            }));

            const promises = interactions.map(interaction =>
                slashCommandHandler.handle(interaction)
            );

            const results = await Promise.allSettled(promises);

            // Check that all promises resolved successfully
            results.forEach(result => {
                expect(result.status).toBe('fulfilled');
            });

            const command = mockClient.commands.get('ping');
            expect(command.execute).toHaveBeenCalledTimes(5);
        });
    });
});
