import { Em<PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, <PERSON>ton<PERSON>uilder, ButtonStyle } from 'discord.js';
import { logger } from '../utils/logger.js';
import ShoppingCart from '../models/ShoppingCart.js';
import { COLORS, EMOJIS, BOT_CONFIG } from '../config/constants.js';

/**
 * Gerenciador de embed fixo do carrinho de compras
 */
export class FixedCartEmbedHandler {
    constructor() {
        this.cartEmbeds = new Map(); // Map channelId -> messageId do embed fixo
    }

    /**
     * Cria ou atualiza o embed fixo do carrinho
     * @param {Object} channel - Canal do Discord
     * @param {Object} cart - Carrinho de compras
     * @param {Object} options - Opções adicionais
     * @returns {Promise<Object>} - Mensagem do embed
     */
    async createOrUpdateCartEmbed(channel, cart, options = {}) {
        try {
            const embed = this.buildCartEmbed(cart, options);
            const components = this.buildCartComponents(cart, options);
            const { paymentInfo } = options;

            // Prepara dados da mensagem
            const messageData = {
                embeds: [embed],
                components: components
            };

            // Adiciona QR code como anexo se disponível
            if (paymentInfo && paymentInfo.qrCodeAttachment) {
                messageData.files = [paymentInfo.qrCodeAttachment];
                // Define a imagem do QR code como imagem do embed
                embed.setImage('attachment://qr-code-pix.png');
            }

            const existingMessageId = this.cartEmbeds.get(channel.id);
            
            if (existingMessageId) {
                // Tenta atualizar embed existente
                try {
                    const existingMessage = await channel.messages.fetch(existingMessageId);
                    const updatedMessage = await existingMessage.edit(messageData);
                    
                    logger.info(`Embed do carrinho atualizado no canal ${channel.id}`);
                    return updatedMessage;
                    
                } catch (error) {
                    // Se não conseguir atualizar, remove da cache e cria novo
                    logger.warn(`Não foi possível atualizar embed existente: ${error.message}`);
                    this.cartEmbeds.delete(channel.id);
                }
            }

            // Cria novo embed
            const newMessage = await channel.send(messageData);

            // Armazena referência do novo embed
            this.cartEmbeds.set(channel.id, newMessage.id);
            
            logger.info(`Novo embed do carrinho criado no canal ${channel.id}`);
            return newMessage;

        } catch (error) {
            logger.error('Erro ao criar/atualizar embed do carrinho:', error);
            throw error;
        }
    }

    /**
     * Constrói o embed do carrinho
     * @param {Object} cart - Carrinho de compras
     * @param {Object} options - Opções adicionais
     * @returns {EmbedBuilder} - Embed construído
     */
    buildCartEmbed(cart, options = {}) {
        const { paymentInfo = null, isPaymentPending = false } = options;

        const embed = new EmbedBuilder()
            .setColor(isPaymentPending ? COLORS.WARNING : COLORS.PRIMARY)
            .setTimestamp();

        // Carrinho vazio
        if (!cart || !cart.items || cart.items.length === 0) {
            return embed
                .setTitle(`${EMOJIS.CART} Carrinho de Compras`)
                .setDescription(
                    `${EMOJIS.INFO} **Seu carrinho está vazio**\n\n` +
                    `Para adicionar itens ao carrinho:\n` +
                    `• Use o comando de produtos da loja\n` +
                    `• Selecione os itens desejados\n` +
                    `• Eles aparecerão aqui automaticamente`
                )
                .setFooter({
                    text: `Loja: ${cart?.storeName || 'N/A'} | Sessão ativa`
                });
        }

        // Carrinho com itens
        let description = `${EMOJIS.CART} **Itens no seu carrinho:**\n\n`;
        
        cart.items.forEach((item, index) => {
            const emoji = item.emoji || '📦';
            description += `${emoji} **${item.productName}**\n`;
            description += `   • Quantidade: **${item.quantity}x**\n`;
            description += `   • Preço unitário: **${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${item.unitPrice.toFixed(2)}**\n`;
            description += `   • Subtotal: **${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${item.totalPrice.toFixed(2)}**\n\n`;
        });

        // Informações de pagamento PIX
        if (paymentInfo && isPaymentPending) {
            description += `${EMOJIS.PIX || '💳'} **Pagamento PIX Gerado**\n\n`;
            description += `💰 **Valor:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${paymentInfo.amount.toFixed(2)}\n`;
            description += `📱 **QR Code:** Escaneie o código na imagem abaixo\n`;
            description += `⏰ **Expira em:** <t:${Math.floor(new Date(paymentInfo.expirationDate).getTime() / 1000)}:R>\n`;
            
            // Status do pagamento baseado no cart.paymentStatus ou padrão
            const statusEmoji = cart?.paymentStatus === 'pending' ? '⏳' : 
                               cart?.paymentStatus === 'approved' ? '✅' : 
                               cart?.paymentStatus === 'cancelled' ? '🚫' : 
                               cart?.paymentStatus === 'expired' ? '⏰' : '⏳';
            const statusText = cart?.paymentStatus === 'pending' ? 'Aguardando Pagamento' : 
                              cart?.paymentStatus === 'approved' ? 'Pagamento Aprovado' : 
                              cart?.paymentStatus === 'cancelled' ? 'Pagamento Cancelado' : 
                              cart?.paymentStatus === 'expired' ? 'Pagamento Expirado' : 'Aguardando Pagamento';
            
            description += `${statusEmoji} **Status:** ${statusText}\n\n`;
            
            if (cart?.paymentStatus === 'pending' || !cart?.paymentStatus || cart?.paymentStatus === 'none') {
                description += `📋 **Como pagar:**\n`;
                description += `• Abra seu app do banco\n`;
                description += `• Escaneie o QR Code abaixo\n`;
                description += `• Confirme o pagamento\n\n`;
            }
        }

        embed
            .setTitle(`${EMOJIS.CART} Carrinho de Compras`)
            .setDescription(description)
            .addFields(
                {
                    name: '📊 Resumo',
                    value: 
                        `**Itens:** ${cart.itemCount}\n` +
                        `**Total:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${cart.subtotal.toFixed(2)}`,
                    inline: true
                },
                {
                    name: '🏪 Loja',
                    value: cart.storeName,
                    inline: true
                }
            )
            .setFooter({
                text: `Pedido expira em 15 minutos | ${new Date().toLocaleString('pt-BR')}`
            });

        // Link alternativo do ticket se disponível (como fallback)
        if (paymentInfo && paymentInfo.ticketUrl && !paymentInfo.qrCodeAttachment) {
            embed.addFields({
                name: '🔗 Link Alternativo do Pagamento',
                value: `[Clique aqui para pagar](${paymentInfo.ticketUrl})`,
                inline: false
            });
        }

        return embed;
    }

    /**
     * Constrói os componentes (botões) do carrinho
     * @param {Object} cart - Carrinho de compras
     * @param {Object} options - Opções adicionais
     * @returns {Array} - Array de ActionRows
     */
    buildCartComponents(cart, options = {}) {
        const { isPaymentPending = false, paymentInfo = null } = options;

        const components = [];

        // Se está aguardando pagamento, mostra botões de verificar status e cancelar
        if (isPaymentPending && cart?.paymentStatus === 'pending') {
            const paymentRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('cart_check_payment_status')
                        .setLabel('Verificar Status')
                        .setEmoji('🔍')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('cart_cancel_payment')
                        .setLabel('Cancelar Compra')
                        .setEmoji('🚫')
                        .setStyle(ButtonStyle.Danger)
                );

            components.push(paymentRow);
            return components;
        }
        
        // Se pagamento foi aprovado, cancelado ou expirado, não mostra botões de pagamento
        if (cart?.paymentStatus && ['approved', 'cancelled', 'expired'].includes(cart.paymentStatus)) {
            return components;
        }

        // Carrinho vazio - sem botões
        if (!cart || !cart.items || cart.items.length === 0) {
            return components;
        }

        // Carrinho com itens - botões de ação
        const actionRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('cart_checkout')
                    .setLabel('Finalizar Compra')
                    .setEmoji('💳')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('cart_clear')
                    .setLabel('Cancelar Compra')
                    .setEmoji('🗑️')
                    .setStyle(ButtonStyle.Danger)
            );

        components.push(actionRow);

        // Se há mais de um item, adiciona botões de gerenciamento
        if (cart.items.length > 1) {
            const managementRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('cart_manage_items')
                        .setLabel('Gerenciar Itens')
                        .setEmoji('⚙️')
                        .setStyle(ButtonStyle.Secondary)
                );

            components.push(managementRow);
        }

        return components;
    }

    /**
     * Remove o embed fixo do carrinho
     * @param {string} channelId - ID do canal
     * @returns {Promise<boolean>} - True se removido com sucesso
     */
    async removeCartEmbed(channelId) {
        try {
            const messageId = this.cartEmbeds.get(channelId);
            if (messageId) {
                this.cartEmbeds.delete(channelId);
                logger.info(`Referência do embed removida para canal ${channelId}`);
                return true;
            }
            return false;
        } catch (error) {
            logger.error('Erro ao remover embed do carrinho:', error);
            return false;
        }
    }

    /**
     * Limpa todas as referências de embeds
     */
    clearAllEmbeds() {
        this.cartEmbeds.clear();
        logger.info('Todas as referências de embeds foram limpas');
    }

    /**
     * Obtém o ID da mensagem do embed para um canal
     * @param {string} channelId - ID do canal
     * @returns {string|null} - ID da mensagem ou null
     */
    getEmbedMessageId(channelId) {
        return this.cartEmbeds.get(channelId) || null;
    }

    /**
     * Verifica se existe um embed ativo para o canal
     * @param {string} channelId - ID do canal
     * @returns {boolean} - True se existe embed ativo
     */
    hasActiveEmbed(channelId) {
        return this.cartEmbeds.has(channelId);
    }
}

// Instância singleton
export const fixedCartEmbedHandler = new FixedCartEmbedHandler();
