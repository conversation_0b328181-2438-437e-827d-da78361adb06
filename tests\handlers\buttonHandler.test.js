/**
 * Tests for button handler parsing logic
 */

describe('Button Handler Parsing Logic', () => {
    
    // Test the parsing logic directly
    function parseCustomId(customId) {
        const parts = customId.split('_');
        const category = parts[0];
        
        let action, id;
        
        if (category === 'config') {
            // Para botões de config, junta todas as partes após 'config_' como action
            action = parts.slice(1).join('_');
            id = null;
        } else {
            // Para outros botões, mantém o formato original
            action = parts[1];
            id = parts[2];
        }
        
        return { category, action, id };
    }

    describe('Config Button Parsing', () => {
        test('should correctly parse config_admin_logs button', () => {
            const result = parseCustomId('config_admin_logs');
            
            expect(result.category).toBe('config');
            expect(result.action).toBe('admin_logs');
            expect(result.id).toBe(null);
        });

        test('should correctly parse config_public_logs button', () => {
            const result = parseCustomId('config_public_logs');
            
            expect(result.category).toBe('config');
            expect(result.action).toBe('public_logs');
            expect(result.id).toBe(null);
        });

        test('should correctly parse config_rate_limit button', () => {
            const result = parseCustomId('config_rate_limit');
            
            expect(result.category).toBe('config');
            expect(result.action).toBe('rate_limit');
            expect(result.id).toBe(null);
        });

        test('should correctly parse config_view_logs button', () => {
            const result = parseCustomId('config_view_logs');
            
            expect(result.category).toBe('config');
            expect(result.action).toBe('view_logs');
            expect(result.id).toBe(null);
        });

        test('should correctly parse config_mercadopago button', () => {
            const result = parseCustomId('config_mercadopago');
            
            expect(result.category).toBe('config');
            expect(result.action).toBe('mercadopago');
            expect(result.id).toBe(null);
        });

        test('should correctly parse config_reset_all button', () => {
            const result = parseCustomId('config_reset_all');
            
            expect(result.category).toBe('config');
            expect(result.action).toBe('reset_all');
            expect(result.id).toBe(null);
        });
    });

    describe('Other Button Types', () => {
        test('should correctly parse store buttons with standard format', () => {
            const result = parseCustomId('store_buy_123');
            
            expect(result.category).toBe('store');
            expect(result.action).toBe('buy');
            expect(result.id).toBe('123');
        });

        test('should correctly parse admin buttons with standard format', () => {
            const result = parseCustomId('admin_approve_456');
            
            expect(result.category).toBe('admin');
            expect(result.action).toBe('approve');
            expect(result.id).toBe('456');
        });
    });

    describe('Edge Cases', () => {
        test('should handle unknown button categories', () => {
            const result = parseCustomId('unknown_action_id');
            
            expect(result.category).toBe('unknown');
            expect(result.action).toBe('action');
            expect(result.id).toBe('id');
        });

        test('should handle single part customId', () => {
            const result = parseCustomId('singlepart');
            
            expect(result.category).toBe('singlepart');
            expect(result.action).toBe(undefined);
            expect(result.id).toBe(undefined);
        });

        test('should handle two part customId', () => {
            const result = parseCustomId('category_action');
            
            expect(result.category).toBe('category');
            expect(result.action).toBe('action');
            expect(result.id).toBe(undefined);
        });
    });
});