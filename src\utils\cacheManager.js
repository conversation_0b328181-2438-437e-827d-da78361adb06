import NodeCache from 'node-cache';
import { logger } from './logger.js';
import performanceMonitor, { cacheMiddleware } from './performanceMonitor.js';

/**
 * Enhanced Caching System for Discord Bot
 * 
 * Provides multi-tier caching for:
 * - Bot configurations
 * - User permissions and roles
 * - Product catalogs and store information
 * - Frequently accessed data
 */

class CacheManager {
    constructor() {
        // Different cache instances for different data types with appropriate TTL
        this.caches = {
            // Bot configurations - long TTL (1 hour)
            config: new NodeCache({ 
                stdTTL: 3600, 
                checkperiod: 600,
                useClones: false,
                maxKeys: 1000
            }),
            
            // User permissions - medium TTL (30 minutes)
            permissions: new NodeCache({ 
                stdTTL: 1800, 
                checkperiod: 300,
                useClones: false,
                maxKeys: 5000
            }),
            
            // Product catalogs - short TTL (10 minutes)
            products: new NodeCache({ 
                stdTTL: 600, 
                checkperiod: 120,
                useClones: false,
                maxKeys: 2000
            }),
            
            // Store information - medium TTL (20 minutes)
            stores: new NodeCache({ 
                stdTTL: 1200, 
                checkperiod: 240,
                useClones: false,
                maxKeys: 500
            }),
            
            // User data - short TTL (5 minutes)
            users: new NodeCache({ 
                stdTTL: 300, 
                checkperiod: 60,
                useClones: false,
                maxKeys: 10000
            }),
            
            // Stock items - very short TTL (2 minutes)
            stock: new NodeCache({ 
                stdTTL: 120, 
                checkperiod: 30,
                useClones: false,
                maxKeys: 5000
            })
        };
        
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0
        };
        
        this.setupEventListeners();
        this.startPeriodicCleanup();
    }
    
    setupEventListeners() {
        // Setup event listeners for all caches
        Object.entries(this.caches).forEach(([name, cache]) => {
            cache.on('set', (key, value) => {
                this.stats.sets++;
                logger.debug(`Cache SET: ${name}:${key}`);
            });
            
            cache.on('del', (key, value) => {
                this.stats.deletes++;
                logger.debug(`Cache DEL: ${name}:${key}`);
            });
            
            cache.on('expired', (key, value) => {
                logger.debug(`Cache EXPIRED: ${name}:${key}`);
            });
        });
    }
    
    startPeriodicCleanup() {
        // Clean up cache statistics every hour
        setInterval(() => {
            this.logStatistics();
            this.resetStatistics();
        }, 3600000); // 1 hour
    }
    
    /**
     * Get value from cache
     * @param {string} cacheType - Type of cache (config, permissions, products, etc.)
     * @param {string} key - Cache key
     * @returns {any|null} Cached value or null if not found
     */
    get(cacheType, key) {
        if (!this.caches[cacheType]) {
            logger.warn(`Invalid cache type: ${cacheType}`);
            return null;
        }
        
        const value = this.caches[cacheType].get(key);
        
        if (value !== undefined) {
            this.stats.hits++;
            cacheMiddleware.hit(key, cacheType);
            logger.debug(`Cache HIT: ${cacheType}:${key}`);
            return value;
        } else {
            this.stats.misses++;
            cacheMiddleware.miss(key, cacheType);
            logger.debug(`Cache MISS: ${cacheType}:${key}`);
            return null;
        }
    }
    
    /**
     * Set value in cache
     * @param {string} cacheType - Type of cache
     * @param {string} key - Cache key
     * @param {any} value - Value to cache
     * @param {number} ttl - Optional TTL override
     */
    set(cacheType, key, value, ttl = null) {
        if (!this.caches[cacheType]) {
            logger.warn(`Invalid cache type: ${cacheType}`);
            return false;
        }
        
        const success = ttl 
            ? this.caches[cacheType].set(key, value, ttl)
            : this.caches[cacheType].set(key, value);
            
        if (success) {
            cacheMiddleware.set(key, ttl);
            logger.debug(`Cache SET: ${cacheType}:${key}`);
        }
        
        return success;
    }
    
    /**
     * Delete value from cache
     * @param {string} cacheType - Type of cache
     * @param {string} key - Cache key
     */
    del(cacheType, key) {
        if (!this.caches[cacheType]) {
            logger.warn(`Invalid cache type: ${cacheType}`);
            return false;
        }
        
        const deleted = this.caches[cacheType].del(key);
        
        if (deleted > 0) {
            cacheMiddleware.delete(key);
        }
        
        return deleted;
    }
    
    /**
     * Clear entire cache type
     * @param {string} cacheType - Type of cache to clear
     */
    clear(cacheType) {
        if (!this.caches[cacheType]) {
            logger.warn(`Invalid cache type: ${cacheType}`);
            return false;
        }
        
        this.caches[cacheType].flushAll();
        logger.info(`Cleared cache: ${cacheType}`);
        return true;
    }
    
    /**
     * Clear all caches
     */
    clearAll() {
        Object.keys(this.caches).forEach(cacheType => {
            this.caches[cacheType].flushAll();
        });
        logger.info('Cleared all caches');
    }
    
    /**
     * Get cache statistics
     */
    getStatistics() {
        const cacheStats = {};
        
        Object.entries(this.caches).forEach(([name, cache]) => {
            const stats = cache.getStats();
            cacheStats[name] = {
                keys: stats.keys,
                hits: stats.hits,
                misses: stats.misses,
                ksize: stats.ksize,
                vsize: stats.vsize
            };
        });
        
        return {
            global: this.stats,
            caches: cacheStats,
            hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0
        };
    }
    
    logStatistics() {
        const stats = this.getStatistics();
        logger.info('Cache Statistics:', {
            hitRate: `${(stats.hitRate * 100).toFixed(2)}%`,
            totalHits: stats.global.hits,
            totalMisses: stats.global.misses,
            cacheDetails: stats.caches
        });
    }
    
    resetStatistics() {
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0
        };
    }
    
    /**
     * Get or set pattern - fetch from cache or execute function and cache result
     * @param {string} cacheType - Type of cache
     * @param {string} key - Cache key
     * @param {Function} fetchFunction - Function to execute if cache miss
     * @param {number} ttl - Optional TTL override
     */
    async getOrSet(cacheType, key, fetchFunction, ttl = null) {
        // Try to get from cache first
        let value = this.get(cacheType, key);
        
        if (value !== null) {
            return value;
        }
        
        // Cache miss - fetch data
        try {
            value = await fetchFunction();
            
            if (value !== null && value !== undefined) {
                this.set(cacheType, key, value, ttl);
            }
            
            return value;
        } catch (error) {
            logger.error(`Error in getOrSet for ${cacheType}:${key}:`, error);
            throw error;
        }
    }
    
    /**
     * Invalidate cache entries by pattern
     * @param {string} cacheType - Type of cache
     * @param {string} pattern - Pattern to match keys (supports wildcards)
     */
    invalidatePattern(cacheType, pattern) {
        if (!this.caches[cacheType]) {
            logger.warn(`Invalid cache type: ${cacheType}`);
            return 0;
        }
        
        const cache = this.caches[cacheType];
        const keys = cache.keys();
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        
        let deletedCount = 0;
        keys.forEach(key => {
            if (regex.test(key)) {
                cache.del(key);
                deletedCount++;
            }
        });
        
        logger.debug(`Invalidated ${deletedCount} keys matching pattern: ${pattern}`);
        return deletedCount;
    }
    
    /**
     * Warm up cache with frequently accessed data
     */
    async warmUp() {
        logger.info('Starting cache warm-up...');
        
        try {
            // Import models dynamically to avoid circular dependencies
            const { default: BotConfig } = await import('../models/BotConfig.js');
            const { default: Store } = await import('../models/Store.js');
            
            // Warm up bot configurations
            const configs = await BotConfig.find({ isActive: true }).lean();
            configs.forEach(config => {
                this.set('config', `guild:${config.guildId}`, config);
            });
            
            // Warm up active stores
            const stores = await Store.find({ isActive: true }).lean();
            stores.forEach(store => {
                this.set('stores', `store:${store._id}`, store);
                this.set('stores', `guild:${store.guildId}`, store);
            });
            
            logger.info(`Cache warm-up completed: ${configs.length} configs, ${stores.length} stores`);
        } catch (error) {
            logger.error('Cache warm-up failed:', error);
        }
    }
}

// Create singleton instance
const cacheManager = new CacheManager();

// Helper functions for common cache operations
export const cache = {
    // Bot configuration helpers
    getBotConfig: (guildId) => cacheManager.get('config', `guild:${guildId}`),
    setBotConfig: (guildId, config) => cacheManager.set('config', `guild:${guildId}`, config),
    
    // User permission helpers
    getUserPermissions: (userId) => cacheManager.get('permissions', `user:${userId}`),
    setUserPermissions: (userId, permissions) => cacheManager.set('permissions', `user:${userId}`, permissions),
    
    // Product helpers
    getProduct: (productId) => cacheManager.get('products', `product:${productId}`),
    setProduct: (productId, product) => cacheManager.set('products', `product:${productId}`, product),
    getStoreProducts: (storeId) => cacheManager.get('products', `store:${storeId}:products`),
    setStoreProducts: (storeId, products) => cacheManager.set('products', `store:${storeId}:products`, products),
    
    // Store helpers
    getStore: (storeId) => cacheManager.get('stores', `store:${storeId}`),
    setStore: (storeId, store) => cacheManager.set('stores', `store:${storeId}`, store),
    
    // User helpers
    getUser: (userId) => cacheManager.get('users', `user:${userId}`),
    setUser: (userId, user) => cacheManager.set('users', `user:${userId}`, user),
    
    // Stock helpers
    getStock: (productId) => cacheManager.get('stock', `product:${productId}:stock`),
    setStock: (productId, stock) => cacheManager.set('stock', `product:${productId}:stock`, stock),
    
    // Generic helpers
    getOrSet: (cacheType, key, fetchFunction, ttl) => cacheManager.getOrSet(cacheType, key, fetchFunction, ttl),
    invalidate: (cacheType, key) => cacheManager.del(cacheType, key),
    invalidatePattern: (cacheType, pattern) => cacheManager.invalidatePattern(cacheType, pattern),
    clear: (cacheType) => cacheManager.clear(cacheType),
    clearAll: () => cacheManager.clearAll(),
    getStats: () => cacheManager.getStatistics(),
    warmUp: () => cacheManager.warmUp()
};

export default cacheManager;