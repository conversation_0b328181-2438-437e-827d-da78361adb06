/**
 * Tests for Store model
 */

describe('Store Model', () => {
    let Store;
    let mockStoreInstance;

    beforeAll(async () => {
        // Mock store instance
        mockStoreInstance = {
            name: 'Test Store',
            description: 'A test store for testing purposes',
            banner: 'https://example.com/banner.jpg',
            color: '#FF5733',
            guildId: '987654321',
            channelId: '123456789',
            ownerId: '555666777',
            isActive: true,
            settings: {
                allowPublicView: true,
                requireApproval: false,
                maxProductsPerUser: 10,
                currency: 'BRL',
                timezone: 'America/Sao_Paulo'
            },
            stats: {
                totalProducts: 5,
                totalSales: 100,
                totalRevenue: 1500.50,
                totalViews: 250
            },
            createdAt: new Date(),
            updatedAt: new Date(),
            save: jest.fn().mockResolvedValue(true),
            updateStats: jest.fn(),
            addProduct: jest.fn(),
            removeProduct: jest.fn(),
            toggleActive: jest.fn()
        };

        // Mock Store model
        Store = {
            findById: jest.fn(),
            findOne: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            findByGuild: jest.fn(),
            findByOwner: jest.fn(),
            getActiveStores: jest.fn(),
            getStoreStats: jest.fn()
        };
    });

    describe('Schema Validation', () => {
        test('should require name', () => {
            const storeData = { description: 'Test', guildId: '123', channelId: '456', ownerId: '789' };
            expect(storeData.name).toBeUndefined();
        });

        test('should require guildId', () => {
            const storeData = { name: 'Test Store', description: 'Test', channelId: '456', ownerId: '789' };
            expect(storeData.guildId).toBeUndefined();
        });

        test('should require channelId', () => {
            const storeData = { name: 'Test Store', description: 'Test', guildId: '123', ownerId: '789' };
            expect(storeData.channelId).toBeUndefined();
        });

        test('should require ownerId', () => {
            const storeData = { name: 'Test Store', description: 'Test', guildId: '123', channelId: '456' };
            expect(storeData.ownerId).toBeUndefined();
        });

        test('should have default values', () => {
            expect(mockStoreInstance.isActive).toBe(true);
            expect(mockStoreInstance.settings.allowPublicView).toBe(true);
            expect(mockStoreInstance.settings.requireApproval).toBe(false);
            expect(mockStoreInstance.settings.maxProductsPerUser).toBe(10);
            expect(mockStoreInstance.settings.currency).toBe('BRL');
            expect(mockStoreInstance.settings.timezone).toBe('America/Sao_Paulo');
        });

        test('should validate name length', () => {
            const shortName = 'A';
            const longName = 'A'.repeat(101);
            const validName = 'Valid Store Name';
            
            expect(shortName.length).toBeLessThan(2);
            expect(longName.length).toBeGreaterThan(100);
            expect(validName.length).toBeGreaterThanOrEqual(2);
            expect(validName.length).toBeLessThanOrEqual(100);
        });

        test('should validate description length', () => {
            const longDescription = 'A'.repeat(1001);
            const validDescription = 'A valid description';
            
            expect(longDescription.length).toBeGreaterThan(1000);
            expect(validDescription.length).toBeLessThanOrEqual(1000);
        });

        test('should validate color format', () => {
            const validColors = ['#FF5733', '#000000', '#FFFFFF'];
            const invalidColors = ['FF5733', '#GG5733', 'red', '#12345'];
            
            validColors.forEach(color => {
                expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
            });
            
            invalidColors.forEach(color => {
                expect(color).not.toMatch(/^#[0-9A-Fa-f]{6}$/);
            });
        });

        test('should validate currency enum', () => {
            const validCurrencies = ['BRL', 'USD', 'EUR'];
            validCurrencies.forEach(currency => {
                expect(validCurrencies).toContain(currency);
            });
        });

        test('should validate maxProductsPerUser range', () => {
            const validMax = 10;
            const invalidMax = -1;
            const tooHighMax = 1001;
            
            expect(validMax).toBeGreaterThanOrEqual(1);
            expect(validMax).toBeLessThanOrEqual(1000);
            expect(invalidMax).toBeLessThan(1);
            expect(tooHighMax).toBeGreaterThan(1000);
        });
    });

    describe('Instance Methods', () => {
        test('updateStats should update store statistics', async () => {
            const newStats = {
                totalProducts: 10,
                totalSales: 150,
                totalRevenue: 2000.75,
                totalViews: 300
            };
            
            mockStoreInstance.updateStats = jest.fn().mockImplementation(function(stats) {
                Object.assign(this.stats, stats);
                this.updatedAt = new Date();
                return this.save();
            });

            await mockStoreInstance.updateStats(newStats);
            
            expect(mockStoreInstance.updateStats).toHaveBeenCalledWith(newStats);
            expect(mockStoreInstance.save).toHaveBeenCalled();
        });

        test('addProduct should increment totalProducts', async () => {
            const initialProducts = mockStoreInstance.stats.totalProducts;
            
            mockStoreInstance.addProduct = jest.fn().mockImplementation(function() {
                this.stats.totalProducts += 1;
                this.updatedAt = new Date();
                return this.save();
            });

            await mockStoreInstance.addProduct();
            
            expect(mockStoreInstance.addProduct).toHaveBeenCalled();
            expect(mockStoreInstance.stats.totalProducts).toBe(initialProducts + 1);
        });

        test('removeProduct should decrement totalProducts', async () => {
            mockStoreInstance.stats.totalProducts = 5;
            
            mockStoreInstance.removeProduct = jest.fn().mockImplementation(function() {
                if (this.stats.totalProducts > 0) {
                    this.stats.totalProducts -= 1;
                    this.updatedAt = new Date();
                    return this.save();
                }
                throw new Error('Nenhum produto para remover');
            });

            await mockStoreInstance.removeProduct();
            
            expect(mockStoreInstance.removeProduct).toHaveBeenCalled();
            expect(mockStoreInstance.stats.totalProducts).toBe(4);
        });

        test('removeProduct should handle zero products', async () => {
            mockStoreInstance.stats.totalProducts = 0;

            mockStoreInstance.removeProduct = jest.fn().mockImplementation(function() {
                if (this.stats.totalProducts > 0) {
                    this.stats.totalProducts -= 1;
                    this.updatedAt = new Date();
                    return this.save();
                }
                throw new Error('Nenhum produto para remover');
            });

            try {
                await mockStoreInstance.removeProduct();
                fail('Should have thrown an error');
            } catch (error) {
                expect(error.message).toBe('Nenhum produto para remover');
            }
        });

        test('toggleActive should switch isActive status', async () => {
            const initialStatus = mockStoreInstance.isActive;
            
            mockStoreInstance.toggleActive = jest.fn().mockImplementation(function() {
                this.isActive = !this.isActive;
                this.updatedAt = new Date();
                return this.save();
            });

            await mockStoreInstance.toggleActive();
            
            expect(mockStoreInstance.toggleActive).toHaveBeenCalled();
            expect(mockStoreInstance.isActive).toBe(!initialStatus);
        });
    });

    describe('Static Methods', () => {
        test('findByGuild should find stores by guild ID', async () => {
            const guildId = '987654321';
            const mockStores = [mockStoreInstance];
            
            Store.findByGuild = jest.fn().mockResolvedValue(mockStores);
            
            const result = await Store.findByGuild(guildId);
            
            expect(Store.findByGuild).toHaveBeenCalledWith(guildId);
            expect(result).toEqual(mockStores);
        });

        test('findByOwner should find stores by owner ID', async () => {
            const ownerId = '555666777';
            const mockStores = [mockStoreInstance];
            
            Store.findByOwner = jest.fn().mockResolvedValue(mockStores);
            
            const result = await Store.findByOwner(ownerId);
            
            expect(Store.findByOwner).toHaveBeenCalledWith(ownerId);
            expect(result).toEqual(mockStores);
        });

        test('getActiveStores should return only active stores', async () => {
            const activeStores = [mockStoreInstance];
            
            Store.getActiveStores = jest.fn().mockResolvedValue(activeStores);
            
            const result = await Store.getActiveStores();
            
            expect(Store.getActiveStores).toHaveBeenCalled();
            expect(result).toEqual(activeStores);
        });

        test('getStoreStats should return aggregated statistics', async () => {
            const aggregatedStats = {
                totalStores: 10,
                totalProducts: 100,
                totalSales: 500,
                totalRevenue: 15000.50
            };
            
            Store.getStoreStats = jest.fn().mockResolvedValue(aggregatedStats);
            
            const result = await Store.getStoreStats();
            
            expect(Store.getStoreStats).toHaveBeenCalled();
            expect(result).toEqual(aggregatedStats);
        });
    });

    describe('Edge Cases and Error Handling', () => {
        test('should handle duplicate store names in same guild', async () => {
            const duplicateError = new Error('Store name already exists in this guild');
            
            Store.create = jest.fn().mockRejectedValue(duplicateError);
            
            await expect(Store.create({
                name: 'Existing Store',
                guildId: '123',
                channelId: '456',
                ownerId: '789'
            })).rejects.toThrow('Store name already exists in this guild');
        });

        test('should handle invalid guild/channel IDs', () => {
            const invalidIds = ['', null, undefined, 123, {}];

            invalidIds.forEach(id => {
                // Check if ID is valid (is a non-empty string)
                const isValid = typeof id === 'string' && id.length > 0;
                // For invalid IDs, isValid should be false
                expect(isValid).toBeFalsy();
            });
        });

        test('should handle banner URL validation', () => {
            const validUrls = [
                'https://example.com/image.jpg',
                'https://cdn.discord.com/attachments/123/456/image.png'
            ];
            
            const invalidUrls = [
                'not-a-url',
                'ftp://example.com/image.jpg',
                'http://example.com/image.txt'
            ];
            
            validUrls.forEach(url => {
                expect(url).toMatch(/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i);
            });
            
            invalidUrls.forEach(url => {
                expect(url).not.toMatch(/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i);
            });
        });

        test('should handle negative statistics', () => {
            const invalidStats = {
                totalProducts: -1,
                totalSales: -5,
                totalRevenue: -100.50,
                totalViews: -10
            };
            
            Object.values(invalidStats).forEach(value => {
                expect(value).toBeLessThan(0);
            });
        });

        test('should handle concurrent store updates', async () => {
            const store1 = { ...mockStoreInstance };
            const store2 = { ...mockStoreInstance };
            
            store1.updateStats = jest.fn().mockImplementation(function(stats) {
                Object.assign(this.stats, stats);
                return Promise.resolve();
            });
            
            store2.updateStats = jest.fn().mockImplementation(function(stats) {
                Object.assign(this.stats, stats);
                return Promise.resolve();
            });
            
            await Promise.all([
                store1.updateStats({ totalSales: 10 }),
                store2.updateStats({ totalSales: 20 })
            ]);
            
            expect(store1.updateStats).toHaveBeenCalled();
            expect(store2.updateStats).toHaveBeenCalled();
        });

        test('should handle missing required settings', () => {
            const incompleteSettings = {
                allowPublicView: true,
                // missing other required settings
            };
            
            const requiredSettings = ['requireApproval', 'maxProductsPerUser', 'currency', 'timezone'];
            
            requiredSettings.forEach(setting => {
                expect(incompleteSettings[setting]).toBeUndefined();
            });
        });
    });
});
