import { SlashCommandBuilder, PermissionFlagsBits } from 'discord.js';
import { logger } from '../../utils/logger.js';
import ShoppingChannelManager from '../../utils/shoppingChannelManager.js';
import Store from '../../models/Store.js';
import Product from '../../models/Product.js';

export default {
    data: new SlashCommandBuilder()
        .setName('test-cart')
        .setDescription('Testa a criação de canal de carrinho de compras')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    
    async execute(interaction) {
        try {
            await interaction.deferReply({ ephemeral: true });

            // Busca uma loja existente para teste
            const store = await Store.findOne({ guildId: interaction.guild.id, isActive: true });
            if (!store) {
                return await interaction.editReply({
                    content: '❌ Nenhuma loja encontrada neste servidor para teste.'
                });
            }

            // Busca um produto da loja para teste
            const product = await Product.findOne({ storeId: store._id, status: 'active' });
            if (!product) {
                return await interaction.editReply({
                    content: '❌ Nenhum produto encontrado na loja para teste.'
                });
            }

            logger.info('Iniciando teste de criação de canal de carrinho...');
            logger.info(`Loja: ${store.name} (${store._id})`);
            logger.info(`Produto: ${product.name} (${product._id})`);
            logger.info(`Usuário: ${interaction.user.tag} (${interaction.user.id})`);
            logger.info(`Guild: ${interaction.guild.name} (${interaction.guild.id})`);

            // Tenta criar o canal de compras
            const result = await ShoppingChannelManager.createShoppingChannel(
                interaction.guild,
                interaction.user,
                store,
                product
            );

            if (result.success) {
                await interaction.editReply({
                    content: `✅ **Teste bem-sucedido!**\n\n` +
                            `🛒 Canal criado: ${result.channel}\n` +
                            `📦 Produto adicionado: ${product.name}\n` +
                            `🆔 Carrinho ID: ${result.cart._id.toString().slice(-8)}\n\n` +
                            `O canal será automaticamente limpo após 2 horas.`
                });
            } else {
                await interaction.editReply({
                    content: `❌ **Teste falhou!**\n\n` +
                            `**Erro:** ${result.error}\n\n` +
                            `Verifique os logs para mais detalhes.`
                });
            }

        } catch (error) {
            logger.error('Erro no teste de carrinho:', {
                message: error.message,
                stack: error.stack,
                userId: interaction.user.id,
                guildId: interaction.guild.id
            });

            if (interaction.deferred) {
                await interaction.editReply({
                    content: `❌ **Erro durante o teste:**\n\`\`\`\n${error.message}\n\`\`\``
                });
            } else {
                await interaction.reply({
                    content: `❌ **Erro durante o teste:**\n\`\`\`\n${error.message}\n\`\`\``,
                    ephemeral: true
                });
            }
        }
    }
};
