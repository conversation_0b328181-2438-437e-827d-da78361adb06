import mongoose from 'mongoose';

const emojiConfigSchema = new mongoose.Schema({
    // Identificação do servidor
    guildId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    
    // Configurações de emojis personalizados
    // Cada chave corresponde a uma chave do objeto EMOJIS em constants.js
    customEmojis: {
        // Emojis gerais
        SUCCESS: { type: String, default: null },
        ERROR: { type: String, default: null },
        WARNING: { type: String, default: null },
        INFO: { type: String, default: null },
        LOADING: { type: String, default: null },
        
        // Emojis da loja
        MONEY: { type: String, default: null },
        CART: { type: String, default: null },
        PRODUCT: { type: String, default: null },
        DIGITAL: { type: String, default: null },
        PHYSICAL: { type: String, default: null },
        SERVICE: { type: String, default: null },
        SUBSCRIPTION: { type: String, default: null },
        
        // Emojis de navegação
        PREVIOUS: { type: String, default: null },
        NEXT: { type: String, default: null },
        REFRESH: { type: String, default: null },
        HOME: { type: String, default: null },
        BACK: { type: String, default: null },
        
        // Emojis de status
        ONLINE: { type: String, default: null },
        OFFLINE: { type: String, default: null },
        IDLE: { type: String, default: null },
        DND: { type: String, default: null }
    },
    
    // Metadados
    lastModifiedBy: {
        type: String // Discord ID do admin que modificou
    }
}, {
    timestamps: true,
    collection: 'emoji_configs'
});

// Métodos estáticos
emojiConfigSchema.statics.findByGuild = function(guildId) {
    return this.findOne({ guildId });
};

emojiConfigSchema.statics.createOrUpdate = async function(guildId, emojiKey, emojiValue, modifiedBy) {
    const updatePath = `customEmojis.${emojiKey}`;
    const update = {
        [updatePath]: emojiValue,
        lastModifiedBy: modifiedBy,
        updatedAt: new Date()
    };
    
    const config = await this.findOneAndUpdate(
        { guildId },
        update,
        { 
            upsert: true, 
            new: true,
            setDefaultsOnInsert: true
        }
    );
    return config;
};

// Métodos de instância
emojiConfigSchema.methods.updateEmoji = function(emojiKey, emojiValue, modifiedBy) {
    if (!this.customEmojis) {
        this.customEmojis = {};
    }
    this.customEmojis[emojiKey] = emojiValue;
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

emojiConfigSchema.methods.resetEmoji = function(emojiKey, modifiedBy) {
    if (this.customEmojis && this.customEmojis[emojiKey] !== undefined) {
        this.customEmojis[emojiKey] = null;
        this.lastModifiedBy = modifiedBy;
        return this.save();
    }
    return Promise.resolve(this);
};

emojiConfigSchema.methods.resetAllEmojis = function(modifiedBy) {
    const defaultEmojis = {};
    for (const key in this.customEmojis) {
        defaultEmojis[key] = null;
    }
    this.customEmojis = defaultEmojis;
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

export default mongoose.model('EmojiConfig', emojiConfigSchema);
