// Teste de integração do sistema de carrinho de compras
const fs = require('fs');
const path = require('path');

describe('Shopping Cart Integration Tests', () => {
    describe('System Architecture', () => {
        it('should have all required files created', () => {
            // Testa se os arquivos principais foram criados
            const requiredFiles = [
                'src/handlers/fixedCartEmbedHandler.js',
                'src/handlers/cartButtonHandler.js',
                'src/handlers/cartModalHandler.js',
                'src/utils/pixPaymentManager.js'
            ];

            requiredFiles.forEach(filePath => {
                const fullPath = path.join(__dirname, '..', filePath);
                expect(fs.existsSync(fullPath)).toBe(true);
            });
        });

        it('should have updated existing handlers', () => {
            // Verifica se os handlers existentes foram modificados
            const modifiedFiles = [
                'src/handlers/buttonHandler.js',
                'src/handlers/modalHandler.js',
                'src/handlers/shoppingCartHandler.js'
            ];

            modifiedFiles.forEach(filePath => {
                const fullPath = path.join(__dirname, '..', filePath);
                expect(fs.existsSync(fullPath)).toBe(true);

                const content = fs.readFileSync(fullPath, 'utf8');
                // Verifica se contém referências aos novos handlers
                if (filePath.includes('buttonHandler')) {
                    expect(content).toContain('CartButtonHandler');
                }
                if (filePath.includes('modalHandler')) {
                    expect(content).toContain('CartModalHandler');
                }
                if (filePath.includes('shoppingCartHandler')) {
                    expect(content).toContain('fixedCartEmbedHandler');
                }
            });
        });

        it('should have MercadoPago dependency installed', () => {
            const packageJsonPath = path.join(__dirname, '..', 'package.json');
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

            expect(packageJson.dependencies).toHaveProperty('mercadopago');
        });
    });

    describe('File Content Validation', () => {
        it('should have correct button custom IDs in cart handlers', () => {
            const cartButtonHandlerPath = path.join(__dirname, '..', 'src/handlers/cartButtonHandler.js');
            const content = fs.readFileSync(cartButtonHandlerPath, 'utf8');

            // Verifica se contém os custom IDs corretos
            expect(content).toContain('cart_clear');
            expect(content).toContain('cart_checkout');
            expect(content).toContain('cart_cancel_payment');
            expect(content).toContain('cart_manage_items');
        });

        it('should have PIX payment integration methods', () => {
            const pixPaymentManagerPath = path.join(__dirname, '..', 'src/utils/pixPaymentManager.js');
            const content = fs.readFileSync(pixPaymentManagerPath, 'utf8');

            // Verifica se contém métodos essenciais
            expect(content).toContain('createPixPayment');
            expect(content).toContain('checkPaymentStatus');
            expect(content).toContain('startPaymentPolling');
        });

        it('should have fixed embed handler methods', () => {
            const fixedCartEmbedHandlerPath = path.join(__dirname, '..', 'src/handlers/fixedCartEmbedHandler.js');
            const content = fs.readFileSync(fixedCartEmbedHandlerPath, 'utf8');

            // Verifica se contém métodos essenciais
            expect(content).toContain('createOrUpdateCartEmbed');
            expect(content).toContain('buildCartEmbed');
            expect(content).toContain('buildCartComponents');
        });

        it('should have modal handler for cart management', () => {
            const cartModalHandlerPath = path.join(__dirname, '..', 'src/handlers/cartModalHandler.js');
            const content = fs.readFileSync(cartModalHandlerPath, 'utf8');

            // Verifica se contém métodos essenciais
            expect(content).toContain('handleCartManageModal');
            expect(content).toContain('validateQuantity');
        });
    });

    describe('Integration Points', () => {
        it('should have integrated new handlers into existing button handler', () => {
            const buttonHandlerPath = path.join(__dirname, '..', 'src/handlers/buttonHandler.js');
            const content = fs.readFileSync(buttonHandlerPath, 'utf8');

            // Verifica se o buttonHandler foi atualizado para usar os novos handlers
            expect(content).toContain('CartButtonHandler');
            expect(content).toContain('cart_clear');
            expect(content).toContain('cart_checkout');
        });

        it('should have integrated cart modal into existing modal handler', () => {
            const modalHandlerPath = path.join(__dirname, '..', 'src/handlers/modalHandler.js');
            const content = fs.readFileSync(modalHandlerPath, 'utf8');

            // Verifica se o modalHandler foi atualizado
            expect(content).toContain('CartModalHandler');
            expect(content).toContain('cart_manage_modal');
        });

        it('should have updated shopping cart handler to use fixed embed', () => {
            const shoppingCartHandlerPath = path.join(__dirname, '..', 'src/handlers/shoppingCartHandler.js');
            const content = fs.readFileSync(shoppingCartHandlerPath, 'utf8');

            // Verifica se o shoppingCartHandler foi atualizado
            expect(content).toContain('fixedCartEmbedHandler');
            expect(content).toContain('createOrUpdateCartEmbed');
        });
    });
});




