import { Embed<PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder, ModalBuilder, TextInputBuilder, TextInputStyle } from 'discord.js';
import { logger } from '../utils/logger.js';
import ShoppingCart from '../models/ShoppingCart.js';
import Product from '../models/Product.js';
import Store from '../models/Store.js';
import StockItem from '../models/StockItem.js';
import Order from '../models/Order.js';
import User from '../models/User.js';
import ShoppingChannelManager from '../utils/shoppingChannelManager.js';
import { fixedCartEmbedHandler } from './fixedCartEmbedHandler.js';
import { COLORS, EMOJIS, BOT_CONFIG } from '../config/constants.js';

/**
 * Manipulador de carrinho de compras
 */
export class ShoppingCartHandler {
    /**
     * Manipula interações de botões do carrinho
     * @param {ButtonInteraction} interaction 
     */
    static async handleCartButton(interaction) {
        const customId = interaction.customId;
        
        try {
            // Busca o carrinho associado ao canal
            const cart = await ShoppingCart.findByChannel(interaction.channel.id);
            
            if (!cart) {
                return await interaction.reply({
                    content: '❌ Carrinho não encontrado. Este canal pode ter expirado.',
                    ephemeral: true
                });
            }

            // Verifica se o usuário é o dono do carrinho
            if (cart.userId !== interaction.user.id) {
                return await interaction.reply({
                    content: '❌ Este não é seu carrinho de compras.',
                    ephemeral: true
                });
            }

            // Verifica se o carrinho não expirou
            if (cart.expiresAt < new Date()) {
                await cart.markAsAbandoned();
                return await interaction.reply({
                    content: '❌ Este carrinho expirou. Inicie uma nova sessão de compras.',
                    ephemeral: true
                });
            }

            switch (customId) {
                case 'cart_view':
                    await this.handleViewCart(interaction, cart);
                    break;
                case 'cart_add_products':
                    await this.handleAddProducts(interaction, cart);
                    break;
                case 'cart_checkout':
                    await this.handleCheckout(interaction, cart);
                    break;
                case 'cart_clear':
                    await this.handleClearCart(interaction, cart);
                    break;
                default:
                    if (customId.startsWith('cart_remove_')) {
                        const productId = customId.replace('cart_remove_', '');
                        await this.handleRemoveItem(interaction, cart, productId);
                    } else if (customId.startsWith('cart_quantity_')) {
                        const productId = customId.replace('cart_quantity_', '');
                        await this.handleQuantityModal(interaction, cart, productId);
                    } else {
                        await interaction.reply({
                            content: '❌ Ação não reconhecida.',
                            ephemeral: true
                        });
                    }
            }

        } catch (error) {
            await logger.error('Erro ao processar botão do carrinho:', error);
            
            try {
                const errorMessage = {
                    content: '❌ Erro interno. Tente novamente.',
                    ephemeral: true
                };

                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp(errorMessage);
                } else {
                    await interaction.reply(errorMessage);
                }
            } catch (replyError) {
                await logger.error('Erro ao responder após falha no carrinho:', replyError);
            }
        }
    }

    /**
     * Mostra o carrinho atual
     * @param {ButtonInteraction} interaction 
     * @param {ShoppingCart} cart 
     */
    static async handleViewCart(interaction, cart) {
        await interaction.deferReply();

        if (cart.items.length === 0) {
            const embed = new EmbedBuilder()
                .setTitle('🛒 Seu Carrinho')
                .setDescription('Seu carrinho está vazio. Use o botão "Adicionar Produtos" para começar!')
                .setColor(COLORS.WARNING);

            return await interaction.editReply({ embeds: [embed] });
        }

        const embed = new EmbedBuilder()
            .setTitle(`🛒 Seu Carrinho (${cart.itemCount} ${cart.itemCount === 1 ? 'item' : 'itens'})`)
            .setColor(COLORS.PRIMARY)
            .setFooter({
                text: `Total: ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${cart.subtotal.toFixed(2)} | Expira: ${cart.expiresAt.toLocaleString('pt-BR')}`
            });

        // Cria componentes para gerenciar itens
        const components = [];
        const itemsPerRow = 5;
        let currentRow = new ActionRowBuilder();
        let buttonsInRow = 0;

        cart.items.forEach((item, index) => {
            const emoji = item.productEmoji || '📦';
            embed.addFields({
                name: `${emoji} ${item.productName}`,
                value: `**Quantidade:** ${item.quantity}\n**Preço unitário:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${item.unitPrice.toFixed(2)}\n**Total:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${item.totalPrice.toFixed(2)}`,
                inline: true
            });

            // Adiciona botões de gerenciamento para cada item
            if (buttonsInRow < itemsPerRow && components.length < 5) {
                currentRow.addComponents(
                    new ButtonBuilder()
                        .setCustomId(`cart_quantity_${item.productId}`)
                        .setLabel(`${item.quantity}`)
                        .setEmoji('🔢')
                        .setStyle(ButtonStyle.Secondary)
                );

                buttonsInRow++;

                // Se completou uma linha ou é o último item
                if (buttonsInRow === itemsPerRow || index === cart.items.length - 1) {
                    components.push(currentRow);
                    currentRow = new ActionRowBuilder();
                    buttonsInRow = 0;
                }
            }
        });

        // Adiciona linha de ações principais se há espaço
        if (components.length < 5) {
            const actionRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('cart_add_products')
                        .setLabel('Adicionar Produtos')
                        .setEmoji('➕')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('cart_checkout')
                        .setLabel('Finalizar Compra')
                        .setEmoji('💳')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('cart_clear')
                        .setLabel('Limpar Carrinho')
                        .setEmoji('🗑️')
                        .setStyle(ButtonStyle.Danger)
                );
            components.push(actionRow);
        }

        await interaction.editReply({
            embeds: [embed],
            components: components
        });
    }

    /**
     * Mostra produtos disponíveis para adicionar
     * @param {ButtonInteraction} interaction 
     * @param {ShoppingCart} cart 
     */
    static async handleAddProducts(interaction, cart) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Busca produtos da loja
            const products = await Product.find({
                storeId: cart.storeId,
                status: 'active'
            }).sort({ name: 1 });

            if (products.length === 0) {
                return await interaction.editReply({
                    content: '❌ Não há produtos disponíveis nesta loja no momento.'
                });
            }

            // Filtra produtos com estoque disponível
            const availableProducts = [];
            for (const product of products) {
                const stockCount = await StockItem.countByProduct(product._id, 'available');
                if (stockCount > 0) {
                    availableProducts.push({ ...product.toObject(), stockCount });
                }
            }

            if (availableProducts.length === 0) {
                return await interaction.editReply({
                    content: '❌ Todos os produtos estão fora de estoque no momento.'
                });
            }

            // Cria select menu com produtos disponíveis
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('cart_add_product_select')
                .setPlaceholder('Selecione um produto para adicionar ao carrinho...')
                .setMinValues(1)
                .setMaxValues(1);

            // Adiciona até 25 produtos (limite do Discord)
            const productsToShow = availableProducts.slice(0, 25);
            for (const product of productsToShow) {
                selectMenu.addOptions(
                    new StringSelectMenuOptionBuilder()
                        .setLabel(product.name)
                        .setDescription(`${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${product.price.toFixed(2)} • Estoque: ${product.stockCount}`)
                        .setValue(product._id.toString())
                        .setEmoji(product.emoji || '📦')
                );
            }

            const row = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.editReply({
                content: '🛍️ Selecione um produto para adicionar ao seu carrinho:',
                components: [row]
            });

        } catch (error) {
            await logger.error('Erro ao carregar produtos:', error);
            await interaction.editReply({
                content: '❌ Erro ao carregar produtos. Tente novamente.'
            });
        }
    }

    /**
     * Remove item do carrinho
     * @param {ButtonInteraction} interaction 
     * @param {ShoppingCart} cart 
     * @param {string} productId 
     */
    static async handleRemoveItem(interaction, cart, productId) {
        await interaction.deferReply({ ephemeral: true });

        try {
            const item = cart.items.find(item => item.productId.toString() === productId);
            if (!item) {
                return await interaction.editReply({
                    content: '❌ Item não encontrado no carrinho.'
                });
            }

            await cart.removeItem(productId);

            await interaction.editReply({
                content: `✅ **${item.productName}** foi removido do seu carrinho.`
            });

            // Atualiza o embed fixo do carrinho
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(interaction.channel, cart);

        } catch (error) {
            await logger.error('Erro ao remover item:', error);
            await interaction.editReply({
                content: '❌ Erro ao remover item. Tente novamente.'
            });
        }
    }

    /**
     * Mostra modal para alterar quantidade
     * @param {ButtonInteraction} interaction 
     * @param {ShoppingCart} cart 
     * @param {string} productId 
     */
    static async handleQuantityModal(interaction, cart, productId) {
        const item = cart.items.find(item => item.productId.toString() === productId);
        if (!item) {
            return await interaction.reply({
                content: '❌ Item não encontrado no carrinho.',
                ephemeral: true
            });
        }

        const modal = new ModalBuilder()
            .setCustomId(`cart_quantity_modal_${productId}`)
            .setTitle(`Alterar Quantidade - ${item.productName}`);

        const quantityInput = new TextInputBuilder()
            .setCustomId('quantity')
            .setLabel('Nova Quantidade')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Digite a nova quantidade (1-99)')
            .setValue(item.quantity.toString())
            .setMinLength(1)
            .setMaxLength(2)
            .setRequired(true);

        const actionRow = new ActionRowBuilder().addComponents(quantityInput);
        modal.addComponents(actionRow);

        await interaction.showModal(modal);
    }

    /**
     * Limpa o carrinho
     * @param {ButtonInteraction} interaction 
     * @param {ShoppingCart} cart 
     */
    static async handleClearCart(interaction, cart) {
        if (cart.items.length === 0) {
            return await interaction.reply({
                content: '❌ Seu carrinho já está vazio.',
                ephemeral: true
            });
        }

        await cart.clearCart();

        await interaction.reply({
            content: '✅ Seu carrinho foi limpo com sucesso.',
            ephemeral: true
        });

        // Atualiza o embed fixo do carrinho
        await fixedCartEmbedHandler.createOrUpdateCartEmbed(interaction.channel, cart);
    }

    /**
     * Inicia processo de checkout
     * @param {ButtonInteraction} interaction 
     * @param {ShoppingCart} cart 
     */
    static async handleCheckout(interaction, cart) {
        await interaction.deferReply({ ephemeral: true });

        if (cart.items.length === 0) {
            return await interaction.editReply({
                content: '❌ Seu carrinho está vazio. Adicione produtos antes de finalizar a compra.'
            });
        }

        // Verifica disponibilidade de estoque para todos os itens
        const stockValidation = await this.validateCartStock(cart);
        if (!stockValidation.valid) {
            return await interaction.editReply({
                content: `❌ **Erro de estoque:**\n${stockValidation.errors.join('\n')}`
            });
        }

        // Cria embed de confirmação
        const embed = new EmbedBuilder()
            .setTitle('💳 Confirmar Compra')
            .setDescription('Revise seu pedido antes de finalizar:')
            .setColor(COLORS.SUCCESS);

        cart.items.forEach(item => {
            const emoji = item.productEmoji || '📦';
            embed.addFields({
                name: `${emoji} ${item.productName}`,
                value: `Quantidade: ${item.quantity} × ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${item.unitPrice.toFixed(2)} = ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${item.totalPrice.toFixed(2)}`,
                inline: false
            });
        });

        embed.addFields({
            name: '💰 Total',
            value: `**${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${cart.subtotal.toFixed(2)}**`,
            inline: false
        });

        const confirmRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('cart_confirm_purchase')
                    .setLabel('Confirmar Compra')
                    .setEmoji('✅')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('cart_cancel_purchase')
                    .setLabel('Cancelar')
                    .setEmoji('❌')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.editReply({
            embeds: [embed],
            components: [confirmRow]
        });
    }

    /**
     * Valida estoque dos itens do carrinho
     * @param {ShoppingCart} cart
     * @returns {Promise<Object>} Resultado da validação
     */
    static async validateCartStock(cart) {
        const errors = [];

        for (const item of cart.items) {
            const availableStock = await StockItem.countByProduct(item.productId, 'available');

            if (availableStock < item.quantity) {
                errors.push(`• **${item.productName}**: Solicitado ${item.quantity}, disponível ${availableStock}`);
            }
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Manipula seleção de produto para adicionar ao carrinho
     * @param {StringSelectMenuInteraction} interaction
     */
    static async handleProductSelect(interaction) {
        const productId = interaction.values[0];

        try {
            await interaction.deferReply({ ephemeral: true });

            // Busca o carrinho associado ao canal
            const cart = await ShoppingCart.findByChannel(interaction.channel.id);

            if (!cart) {
                return await interaction.editReply({
                    content: '❌ Carrinho não encontrado.'
                });
            }

            // Verifica se o usuário é o dono do carrinho
            if (cart.userId !== interaction.user.id) {
                return await interaction.editReply({
                    content: '❌ Este não é seu carrinho de compras.'
                });
            }

            // Busca o produto
            const product = await Product.findById(productId);
            if (!product) {
                return await interaction.editReply({
                    content: '❌ Produto não encontrado.'
                });
            }

            // Verifica estoque
            const availableStock = await StockItem.countByProduct(productId, 'available');
            if (availableStock <= 0) {
                return await interaction.editReply({
                    content: `❌ **${product.name}** está fora de estoque.`
                });
            }

            // Verifica se já existe no carrinho
            const existingItem = cart.items.find(item =>
                item.productId.toString() === productId
            );

            if (existingItem) {
                // Verifica se pode adicionar mais uma unidade
                if (existingItem.quantity >= availableStock) {
                    return await interaction.editReply({
                        content: `❌ Você já tem a quantidade máxima disponível de **${product.name}** no carrinho.`
                    });
                }

                if (existingItem.quantity >= BOT_CONFIG.STORE.MAX_QUANTITY_PER_ITEM) {
                    return await interaction.editReply({
                        content: `❌ Quantidade máxima por item é ${BOT_CONFIG.STORE.MAX_QUANTITY_PER_ITEM}.`
                    });
                }
            }

            // Verifica limite de itens no carrinho
            if (cart.items.length >= BOT_CONFIG.STORE.MAX_CART_ITEMS && !existingItem) {
                return await interaction.editReply({
                    content: `❌ Limite máximo de ${BOT_CONFIG.STORE.MAX_CART_ITEMS} tipos de produtos no carrinho.`
                });
            }

            // Adiciona produto ao carrinho
            await cart.addItem(product, 1);

            const emoji = product.emoji || '📦';
            await interaction.editReply({
                content: `✅ **${emoji} ${product.name}** foi adicionado ao seu carrinho!\n` +
                        `💰 Preço: ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${product.price.toFixed(2)}`
            });

            // Atualiza o embed fixo do carrinho
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(interaction.channel, cart);

        } catch (error) {
            await logger.error('Erro ao adicionar produto ao carrinho:', error);
            await interaction.editReply({
                content: '❌ Erro ao adicionar produto. Tente novamente.'
            });
        }
    }

    /**
     * Manipula modal de alteração de quantidade
     * @param {ModalSubmitInteraction} interaction
     */
    static async handleQuantityModal(interaction) {
        const productId = interaction.customId.replace('cart_quantity_modal_', '');
        const newQuantity = parseInt(interaction.fields.getTextInputValue('quantity'));

        try {
            await interaction.deferReply({ ephemeral: true });

            // Validação básica
            if (isNaN(newQuantity) || newQuantity < 1 || newQuantity > 99) {
                return await interaction.editReply({
                    content: '❌ Quantidade deve ser um número entre 1 e 99.'
                });
            }

            // Busca o carrinho
            const cart = await ShoppingCart.findByChannel(interaction.channel.id);
            if (!cart) {
                return await interaction.editReply({
                    content: '❌ Carrinho não encontrado.'
                });
            }

            // Verifica se o usuário é o dono do carrinho
            if (cart.userId !== interaction.user.id) {
                return await interaction.editReply({
                    content: '❌ Este não é seu carrinho de compras.'
                });
            }

            // Busca o item no carrinho
            const item = cart.items.find(item => item.productId.toString() === productId);
            if (!item) {
                return await interaction.editReply({
                    content: '❌ Item não encontrado no carrinho.'
                });
            }

            // Verifica estoque disponível
            const availableStock = await StockItem.countByProduct(productId, 'available');
            if (newQuantity > availableStock) {
                return await interaction.editReply({
                    content: `❌ Estoque insuficiente. Disponível: ${availableStock} unidades.`
                });
            }

            // Atualiza quantidade
            await cart.updateItemQuantity(productId, newQuantity);

            await interaction.editReply({
                content: `✅ Quantidade de **${item.productName}** alterada para ${newQuantity}.`
            });

            // Atualiza o embed fixo do carrinho
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(interaction.channel, cart);

        } catch (error) {
            await logger.error('Erro ao alterar quantidade:', error);
            await interaction.editReply({
                content: '❌ Erro ao alterar quantidade. Tente novamente.'
            });
        }
    }

    /**
     * Confirma a compra e cria o pedido
     * @param {ButtonInteraction} interaction
     */
    static async handleConfirmPurchase(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Busca o carrinho
            const cart = await ShoppingCart.findByChannel(interaction.channel.id);
            if (!cart) {
                return await interaction.editReply({
                    content: '❌ Carrinho não encontrado.'
                });
            }

            // Verifica se o usuário é o dono do carrinho
            if (cart.userId !== interaction.user.id) {
                return await interaction.editReply({
                    content: '❌ Este não é seu carrinho de compras.'
                });
            }

            // Valida estoque novamente
            const stockValidation = await this.validateCartStock(cart);
            if (!stockValidation.valid) {
                return await interaction.editReply({
                    content: `❌ **Erro de estoque:**\n${stockValidation.errors.join('\n')}`
                });
            }

            // Busca ou cria usuário
            let user = await User.findOne({ discordId: interaction.user.id });
            if (!user) {
                user = new User({
                    discordId: interaction.user.id,
                    username: interaction.user.username,
                    discriminator: interaction.user.discriminator
                });
                await user.save();
            }

            // Cria o pedido
            const order = new Order({
                userId: user._id,
                customerDiscordId: interaction.user.id,
                subtotal: cart.subtotal,
                total: cart.subtotal,
                status: 'pending',
                paymentStatus: 'pending'
            });

            // Adiciona itens ao pedido e reserva estoque
            for (const cartItem of cart.items) {
                // Reserva itens de estoque
                const stockItems = await StockItem.find({
                    productId: cartItem.productId,
                    status: 'available'
                }).limit(cartItem.quantity);

                if (stockItems.length < cartItem.quantity) {
                    return await interaction.editReply({
                        content: `❌ Estoque insuficiente para **${cartItem.productName}**.`
                    });
                }

                // Reserva os itens
                for (const stockItem of stockItems) {
                    await stockItem.reserve(interaction.user.id, 15); // 15 minutos de reserva
                }

                // Adiciona item ao pedido
                await order.addItem(
                    cartItem.productId,
                    cartItem.productName,
                    cartItem.quantity,
                    cartItem.unitPrice
                );
            }

            await order.save();

            // Marca carrinho como completo
            await cart.markAsCompleted(order._id);

            // Envia confirmação
            const embed = new EmbedBuilder()
                .setTitle('✅ Pedido Criado com Sucesso!')
                .setDescription(
                    `Seu pedido foi criado e os itens foram reservados por 15 minutos.\n\n` +
                    `**Número do Pedido:** \`${order.orderNumber}\`\n` +
                    `**Total:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${order.total.toFixed(2)}\n\n` +
                    `⏰ **Próximos passos:**\n` +
                    `• Complete o pagamento nos próximos 15 minutos\n` +
                    `• Os itens serão liberados automaticamente se não pago\n` +
                    `• Você receberá instruções de pagamento em breve`
                )
                .setColor(COLORS.SUCCESS)
                .setFooter({
                    text: `Pedido: ${order.orderNumber} | ${new Date().toLocaleString('pt-BR')}`
                });

            await interaction.editReply({
                embeds: [embed]
            });

            // Envia mensagem no canal principal
            await interaction.channel.send({
                content: `🎉 **Pedido finalizado!**\n${interaction.user}, seu pedido \`${order.orderNumber}\` foi criado com sucesso.`,
                embeds: [embed]
            });

            // Agenda limpeza do canal em 30 minutos
            setTimeout(async () => {
                try {
                    await ShoppingChannelManager.cleanupShoppingChannel(
                        interaction.channel,
                        'Sessão de compra finalizada'
                    );
                } catch (error) {
                    await logger.error('Erro ao limpar canal após compra:', error);
                }
            }, 30 * 60 * 1000); // 30 minutos

        } catch (error) {
            await logger.error('Erro ao confirmar compra:', error);
            await interaction.editReply({
                content: '❌ Erro ao processar compra. Tente novamente.'
            });
        }
    }
}

export default ShoppingCartHandler;
