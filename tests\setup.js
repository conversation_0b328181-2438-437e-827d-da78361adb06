/**
 * Test setup and configuration
 */

// Load test environment variables if available
try {
    require('dotenv').config({ path: '.env.test' });
} catch (error) {
    // dotenv not available, continue without it
}

// Set test environment
process.env.NODE_ENV = 'test';

// Mock Discord.js client for tests
const mockClient = {
    commands: new Map(),
    guilds: {
        cache: new Map()
    },
    users: {
        cache: new Map()
    },
    channels: {
        cache: new Map()
    },
    user: {
        tag: 'TestBot#1234',
        displayAvatarURL: () => 'https://example.com/avatar.png'
    },
    ws: {
        ping: 50
    }
};

// Mock interaction object
const mockInteraction = {
    user: {
        id: '123456789',
        tag: 'TestUser#1234',
        username: 'TestUser',
        displayAvatarURL: () => 'https://example.com/user-avatar.png'
    },
    guild: {
        id: '987654321',
        name: 'Test Guild',
        members: {
            me: {
                permissions: {
                    has: jest.fn().mockReturnValue(true)
                }
            }
        },
        channels: {
            cache: new Map()
        }
    },
    member: {
        permissions: {
            has: jest.fn().mockReturnValue(true)
        }
    },
    client: mockClient,
    reply: jest.fn(),
    followUp: jest.fn(),
    editReply: jest.fn(),
    deferReply: jest.fn(),
    replied: false,
    deferred: false,
    createdTimestamp: Date.now(),
    commandName: 'test'
};

// Mock database connection
const mockDatabase = {
    connect: jest.fn().mockResolvedValue(true),
    disconnect: jest.fn().mockResolvedValue(true),
    isConnected: jest.fn().mockReturnValue(true)
};

// Global test utilities
global.mockClient = mockClient;
global.mockInteraction = mockInteraction;
global.mockDatabase = mockDatabase;

// Suppress console logs during tests unless explicitly needed
const originalConsole = console;
global.console = {
    ...originalConsole,
    log: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
};

// Restore console for specific tests if needed
global.restoreConsole = () => {
    global.console = originalConsole;
};
