import { SlashCommandBuilder, EmbedBuilder } from 'discord.js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packageJson = JSON.parse(readFileSync(join(__dirname, '../../../package.json'), 'utf8'));
const { version } = packageJson;

export default {
    data: new SlashCommandBuilder()
        .setName('info')
        .setDescription('Mostra informações sobre o bot'),
    
    async execute(interaction) {
        const client = interaction.client;
        
        // Calcula uptime
        const uptime = process.uptime();
        const days = Math.floor(uptime / 86400);
        const hours = Math.floor(uptime / 3600) % 24;
        const minutes = Math.floor(uptime / 60) % 60;
        const seconds = Math.floor(uptime % 60);
        
        const uptimeString = `${days}d ${hours}h ${minutes}m ${seconds}s`;
        
        // Informações de memória
        const memoryUsage = process.memoryUsage();
        const memoryUsed = Math.round(memoryUsage.heapUsed / 1024 / 1024);
        const memoryTotal = Math.round(memoryUsage.heapTotal / 1024 / 1024);
        
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🤖 Informações do Bot')
            .setDescription('Bot Discord para gerenciamento de loja')
            .addFields(
                {
                    name: '📊 Estatísticas',
                    value: [
                        `**Servidores:** ${client.guilds.cache.size}`,
                        `**Usuários:** ${client.users.cache.size}`,
                        `**Comandos:** ${client.commands.size}`,
                        `**Canais:** ${client.channels.cache.size}`
                    ].join('\n'),
                    inline: true
                },
                {
                    name: '⚙️ Sistema',
                    value: [
                        `**Versão:** ${version}`,
                        `**Node.js:** ${process.version}`,
                        `**Discord.js:** ${(await import('discord.js')).version}`,
                        `**Uptime:** ${uptimeString}`
                    ].join('\n'),
                    inline: true
                },
                {
                    name: '💾 Memória',
                    value: [
                        `**Usado:** ${memoryUsed} MB`,
                        `**Total:** ${memoryTotal} MB`,
                        `**Uso:** ${Math.round((memoryUsed / memoryTotal) * 100)}%`
                    ].join('\n'),
                    inline: true
                }
            )
            .setThumbnail(client.user.displayAvatarURL())
            .setTimestamp()
            .setFooter({ 
                text: `Solicitado por ${interaction.user.username}`,
                iconURL: interaction.user.displayAvatarURL()
            });
        
        await interaction.reply({ embeds: [embed] });
    }
};
