# Database Optimization Guide

This document outlines the comprehensive database optimization improvements implemented for the Discord bot, including performance enhancements, caching strategies, and maintenance procedures.

## 🎯 Overview

The optimization package includes:
- **Database cleanup and migration tools**
- **Advanced caching system**
- **Query optimization utilities**
- **Performance monitoring**
- **Pagination for large datasets**
- **Automated maintenance scripts**

## 📁 New Files Structure

```
├── scripts/
│   ├── database-audit.js           # Database analysis and audit
│   ├── database-cleanup.js         # Legacy data cleanup
│   ├── database-migration.js       # Safe migration with backup
│   ├── optimize-database.js        # Comprehensive optimization
│   └── run-optimization.js         # Master optimization runner
├── src/utils/
│   ├── cacheManager.js             # Multi-tier caching system
│   ├── pagination.js               # Advanced pagination utilities
│   ├── queryOptimizer.js           # Query performance optimization
│   └── performanceMonitor.js       # Real-time performance tracking
└── config/
    └── monitoring.json              # Performance monitoring config
```

## 🚀 Quick Start

### 1. Preview Optimization (Recommended First Step)
```bash
# See what changes will be made without applying them
node scripts/run-optimization.js --dry-run --verbose
```

### 2. Run Full Optimization
```bash
# Complete optimization with backup
node scripts/run-optimization.js
```

### 3. Emergency Rollback (if needed)
```bash
# Restore from backup if something goes wrong
node scripts/database-migration.js --restore backup_file.json
```

## 🔧 Individual Tools

### Database Audit
Analyze current database state and identify optimization opportunities:
```bash
node scripts/database-audit.js
```

### Legacy Cleanup
Remove outdated collections and data:
```bash
node scripts/database-cleanup.js --dry-run  # Preview
node scripts/database-cleanup.js            # Execute
```

### Performance Optimization
Optimize indexes and query performance:
```bash
node scripts/optimize-database.js --dry-run --verbose
```

## 📊 Performance Improvements

### 1. Caching System

**Multi-tier caching** with different TTLs for various data types:

```javascript
import cacheManager from './src/utils/cacheManager.js';

// Cache bot configuration (5 minutes TTL)
cacheManager.set('config:guildId', configData, 'config');

// Cache user permissions (10 minutes TTL)
cacheManager.set('permission:userId', permissionData, 'permission');

// Cache product data (30 minutes TTL)
cacheManager.set('product:productId', productData, 'product');
```

**Cache Types:**
- `config`: Bot configurations (300s TTL)
- `permission`: User permissions (600s TTL)
- `product`: Product data (1800s TTL)
- `store`: Store information (1800s TTL)
- `user`: User data (900s TTL)
- `stock`: Stock information (300s TTL)

### 2. Query Optimization

**Optimized aggregation pipelines** for common operations:

```javascript
import { QueryOptimizer } from './src/utils/queryOptimizer.js';

const optimizer = new QueryOptimizer();

// Optimized product search with caching
const products = await optimizer.searchProducts({
    storeId: 'store123',
    category: 'electronics',
    minPrice: 10,
    maxPrice: 100
}, { page: 1, limit: 20 });

// Cached order analytics
const analytics = await optimizer.getOrderAnalytics('daily', 30);
```

### 3. Pagination System

**Advanced pagination** for Discord embeds with navigation:

```javascript
import { PaginationManager } from './src/utils/pagination.js';

const paginator = new PaginationManager({
    itemsPerPage: 10,
    timeout: 300000 // 5 minutes
});

// Paginate stock items
const embed = await paginator.createStockPagination(
    stockItems,
    interaction,
    { page: 1 }
);
```

### 4. Performance Monitoring

**Real-time performance tracking** with alerts:

```javascript
import performanceMonitor from './src/utils/performanceMonitor.js';

// Track database operations
const tracker = trackDatabaseOperation('find', 'products');
// ... perform database operation
tracker.finish(); // Automatically logs performance

// Get performance report
const report = performanceMonitor.generateReport();
console.log('Cache hit rate:', report.cache.hitRate);
console.log('Average query time:', report.database.averageResponseTime);
```

## 📈 Index Optimizations

### Before Optimization
- **78 total indexes** across 17 collections
- Many redundant single-field indexes
- Suboptimal compound indexes

### After Optimization
- **Compound indexes** for common query patterns
- **Removed redundant indexes** to reduce storage
- **Optimized for specific use cases**

**New Optimized Indexes:**

```javascript
// Products collection
{ storeId: 1, isActive: 1, featured: -1, createdAt: -1 }  // Store listings
{ category: 1, price: 1, isActive: 1 }                   // Category filtering
{ isActive: 1, totalSold: -1 }                           // Bestsellers

// Orders collection
{ guildId: 1, status: 1, createdAt: -1 }                 // Guild order history
{ userId: 1, status: 1, createdAt: -1 }                  // User order history
{ status: 1, paymentMethod: 1 }                          // Payment analytics

// Stock Items collection
{ productId: 1, status: 1, createdAt: -1 }               // Product stock
{ storeId: 1, status: 1 }                                // Store inventory

// Users collection
{ discordId: 1, isBlacklisted: 1 }                       // Quick user lookup
{ guildId: 1, vipLevel: -1, totalOrders: -1 }            // VIP user analytics
```

## 🧹 Data Cleanup

### Legacy Collections Removed
- `lojas` (Portuguese stores) → Migrated to `stores`
- `produtos` (Portuguese products) → Migrated to `products`
- `compras` (Portuguese orders) → Migrated to `orders`
- `configuracoes` (Portuguese configs) → Migrated to `bot_configs`
- `pagamentos` (Portuguese payments) → Archived
- `estoque` (Portuguese stock) → Migrated to `stock_items`

### Data Retention Policies
- **Analytics data**: 90 days retention
- **Completed orders**: 1 year retention
- **Error logs**: 30 days retention
- **Performance logs**: 7 days retention

## 📊 Performance Metrics

### Expected Improvements
- **Query response time**: 40-60% faster for common operations
- **Cache hit rate**: 70-85% for frequently accessed data
- **Database size**: 10-20% reduction after cleanup
- **Index efficiency**: 50% fewer indexes, better performance

### Monitoring Alerts
Automatic alerts for:
- Cache hit rate below 70%
- Average query time above 1 second
- Memory usage above 400MB
- Error rate above 5%

## 🔧 Configuration

### Cache Configuration
```javascript
// src/utils/cacheManager.js
const defaultTtls = {
    config: 300,      // 5 minutes
    permission: 600,  // 10 minutes
    product: 1800,    // 30 minutes
    store: 1800,      // 30 minutes
    user: 900,        // 15 minutes
    stock: 300        // 5 minutes
};
```

### Performance Monitoring
```json
{
    "enabled": true,
    "logInterval": 60000,
    "slowQueryThreshold": 1000,
    "cacheHitRateThreshold": 70,
    "memoryThreshold": 400,
    "logFilePath": "./logs/performance.log",
    "alertsEnabled": true
}
```

## 🚨 Troubleshooting

### Common Issues

**1. Cache not working**
```bash
# Check if node-cache is installed
npm list node-cache

# Reinstall if needed
npm install node-cache
```

**2. Performance monitoring errors**
```bash
# Create logs directory
mkdir logs

# Check permissions
ls -la logs/
```

**3. Migration failures**
```bash
# Run in dry-run mode first
node scripts/database-migration.js --dry-run

# Check MongoDB connection
node -e "import mongoose from 'mongoose'; import dotenv from 'dotenv'; dotenv.config(); mongoose.connect(process.env.MONGODB_URI).then(() => console.log('OK')).catch(console.error);"
```

### Recovery Procedures

**1. Restore from backup**
```bash
# List available backups
ls -la backups/

# Restore specific backup
node scripts/database-migration.js --restore backups/migration_1234567890.json
```

**2. Reset cache**
```javascript
import cacheManager from './src/utils/cacheManager.js';
cacheManager.clearAll();
```

**3. Rebuild indexes**
```bash
# Drop and recreate all indexes
node scripts/optimize-database.js --rebuild-indexes
```

## 📋 Maintenance Schedule

### Daily
- Monitor performance metrics
- Check error logs
- Verify cache hit rates

### Weekly
- Review slow queries
- Analyze performance trends
- Clean up old logs

### Monthly
- Run database optimization
- Update retention policies
- Performance tuning review

### Quarterly
- Full database audit
- Index optimization review
- Backup strategy evaluation

## 🔗 Integration Examples

### Using Cache in Commands
```javascript
// Before: Direct database query
const product = await Product.findById(productId);

// After: Cache-first approach
import cacheManager from '../utils/cacheManager.js';

let product = cacheManager.get(`product:${productId}`, 'product');
if (!product) {
    product = await Product.findById(productId);
    if (product) {
        cacheManager.set(`product:${productId}`, product, 'product');
    }
}
```

### Using Pagination in Commands
```javascript
import { createStockPagination } from '../utils/pagination.js';

// In your slash command
const stockItems = await StockItem.find({ storeId }).lean();
const embed = await createStockPagination(stockItems, interaction, {
    page: 1,
    title: 'Store Inventory'
});

await interaction.reply({ embeds: [embed.embed], components: [embed.row] });
```

### Using Query Optimizer
```javascript
import { QueryOptimizer } from '../utils/queryOptimizer.js';

const optimizer = new QueryOptimizer();

// Optimized product search
const results = await optimizer.searchProducts({
    storeId: interaction.options.getString('store'),
    category: interaction.options.getString('category'),
    minPrice: interaction.options.getNumber('min_price'),
    maxPrice: interaction.options.getNumber('max_price')
}, {
    page: interaction.options.getInteger('page') || 1,
    limit: 10
});
```

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the performance logs in `./logs/`
3. Run diagnostics: `node scripts/database-audit.js`
4. Check backup availability in `./backups/`

---

**⚠️ Important Notes:**
- Always run `--dry-run` first to preview changes
- Keep backups for at least 30 days
- Monitor performance after optimization
- Test thoroughly in development before production deployment